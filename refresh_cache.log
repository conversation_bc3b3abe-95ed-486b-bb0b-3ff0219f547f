2025-05-16 20:27:44,403 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:27:44,404 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:27:44,404 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:27:44,406 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:27:44.406022 ---
2025-05-16 20:27:44,406 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:27:44,612 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:27:44,612 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:27:44,613 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:27:44,617 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:27:44,618 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:27:45,068 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:27:45,909 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:27:45,911 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:27:45,998 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:27:46,005 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:27:46,094 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:27:46,293 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:27:46,421 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:27:46,569 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:27:48,428 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:27:48,430 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:27:49,193 - INFO - refresh_cache_script - No new user-specific update messages collected for mohamed.elsaadi in this run. No batch to cache.
2025-05-16 20:27:49,194 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:27:49,195 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:27:49,195 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:27:49,196 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:27:49.196584 (Duration: 0:00:04.790562) ---
2025-05-16 20:27:49,197 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:28:26,445 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:28:26,446 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:28:26,446 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:28:26,448 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:28:26.448144 ---
2025-05-16 20:28:26,448 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:28:26,661 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:28:26,662 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:28:26,663 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:28:26,666 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:28:26,667 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:28:27,179 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:28:27,818 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:28:27,820 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:28:28,006 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:28:28,048 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:28:28,063 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:28:28,096 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:28:28,242 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:28:28,362 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:28:28,665 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:28:28,666 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:28:29,424 - INFO - refresh_cache_script - No new user-specific update messages collected for mohamed.elsaadi in this run. No batch to cache.
2025-05-16 20:28:29,425 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:28:29,426 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:28:29,426 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:28:29,427 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:28:29.427382 (Duration: 0:00:02.979238) ---
2025-05-16 20:28:29,428 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:30:04,032 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:30:04,032 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:30:04,033 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:30:04,034 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:30:04.034534 ---
2025-05-16 20:30:04,035 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:30:04,239 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:30:04,240 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:30:04,241 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:30:04,244 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:30:04,246 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:30:04,766 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:30:05,712 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:30:05,949 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:30:05,951 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:30:05,995 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:30:06,046 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:30:06,057 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:30:06,062 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:30:06,523 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:30:06,582 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:30:06,584 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:30:07,328 - INFO - refresh_cache_script - No new user-specific update messages collected for mohamed.elsaadi in this run. No batch to cache.
2025-05-16 20:30:07,329 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:30:07,330 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:30:07,331 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:30:07,331 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:30:07.331776 (Duration: 0:00:03.297242) ---
2025-05-16 20:30:07,332 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:33:34,147 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:33:34,147 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:33:34,147 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:33:34,149 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:33:34.149282 ---
2025-05-16 20:33:34,149 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:33:34,356 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:33:34,356 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:33:34,357 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:33:34,360 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:33:34,362 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:33:34,927 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:33:35,533 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:33:35,536 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:33:35,540 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:33:35,592 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:33:35,805 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:33:35,978 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:33:36,092 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:33:36,157 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:33:36,214 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:33:36,215 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:33:36,973 - INFO - refresh_cache_script - No new user-specific update messages collected for mohamed.elsaadi in this run. No batch to cache.
2025-05-16 20:33:36,973 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:33:36,974 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:33:36,975 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:33:36,976 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:33:36.975996 (Duration: 0:00:02.826714) ---
2025-05-16 20:33:36,976 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:35:31,644 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:35:31,644 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:35:31,645 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:35:31,647 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:35:31.647428 ---
2025-05-16 20:35:31,648 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:35:31,852 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:35:31,853 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:35:31,853 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:35:31,857 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:35:31,858 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:35:32,252 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:35:33,064 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:35:33,419 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:35:33,528 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:35:33,530 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:35:33,540 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:35:33,548 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:35:33,714 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:35:34,239 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:35:35,824 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:35:35,826 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:35:36,580 - INFO - refresh_cache_script - No new user-specific update messages collected for mohamed.elsaadi in this run. No batch to cache.
2025-05-16 20:35:36,581 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:35:36,582 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:35:36,582 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:35:36,583 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:35:36.583187 (Duration: 0:00:04.935759) ---
2025-05-16 20:35:36,583 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:36:19,281 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:36:19,281 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:36:19,282 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:36:19,283 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:36:19.283776 ---
2025-05-16 20:36:19,284 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:36:19,492 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:36:19,493 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:36:19,493 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:36:19,497 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:36:19,498 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:36:19,949 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:36:20,470 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:36:20,716 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:36:20,717 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:36:20,779 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:36:20,903 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:36:21,029 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:36:21,662 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:36:21,734 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:36:23,776 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:36:23,777 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:36:24,542 - INFO - refresh_cache_script - No new user-specific update messages collected for mohamed.elsaadi in this run. No batch to cache.
2025-05-16 20:36:24,542 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:36:24,543 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:36:24,544 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:36:24,545 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:36:24.545086 (Duration: 0:00:05.261310) ---
2025-05-16 20:36:24,545 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:37:36,944 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:37:36,944 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:37:36,945 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:37:36,946 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:37:36.946771 ---
2025-05-16 20:37:36,947 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:37:37,213 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:37:37,214 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:37:37,215 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:37:37,218 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:37:37,219 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:37:42,375 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:37:57,853 - WARNING - urllib3.connectionpool - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ReadTimeoutError("HTTPSConnectionPool(host='apps.guc.edu.eg', port=443): Read timed out. (read timeout=20)")': /student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:37:57,949 - WARNING - scraping.core - Request failed: 401 Unauthorized for GET https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:37:57,950 - ERROR - scraping.grades - Failed to fetch initial grades page for mohamed.elsaadi.
2025-05-16 20:37:58,158 - WARNING - refresh_cache_script - Refresh task mohamed.elsaadi_grades returned None.
2025-05-16 20:37:58,159 - INFO - refresh_cache_script - No new user-specific update messages collected for mohamed.elsaadi in this run. No batch to cache.
2025-05-16 20:37:58,159 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:37:58,160 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:37:58,160 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: skipped: scraper returned None
2025-05-16 20:37:58,161 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:37:58.161480 (Duration: 0:00:21.214709) ---
2025-05-16 20:37:58,162 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=1, Skipped=1, Failed=0
2025-05-16 20:39:00,777 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:39:00,777 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:39:00,778 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:39:00,779 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:39:00.779735 ---
2025-05-16 20:39:00,780 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:39:01,080 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:39:01,081 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:39:01,081 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:39:01,084 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:39:01,085 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:39:01,559 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:39:02,160 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:39:02,162 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:39:02,182 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:39:02,222 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:39:02,263 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:39:02,530 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:39:02,705 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:39:02,913 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:39:02,916 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:39:02,917 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:39:03,954 - INFO - refresh_cache_script - No new user-specific update messages collected for mohamed.elsaadi in this run. No batch to cache.
2025-05-16 20:39:03,955 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:39:03,956 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:39:03,957 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:39:03,957 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:39:03.957720 (Duration: 0:00:03.177985) ---
2025-05-16 20:39:03,958 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:42:04,764 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:42:04,765 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:42:04,766 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:42:04,767 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:42:04.767491 ---
2025-05-16 20:42:04,768 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:42:05,077 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:42:05,078 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:42:05,079 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:42:05,084 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:42:05,086 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:42:05,665 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:42:06,346 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:42:06,522 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:42:06,524 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:42:06,525 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:42:06,649 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:42:06,847 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:42:06,935 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:42:07,020 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:42:07,132 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:42:07,133 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:42:08,210 - INFO - refresh_cache_script - No new user-specific update messages collected for mohamed.elsaadi in this run. No batch to cache.
2025-05-16 20:42:08,211 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:42:08,212 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:42:08,212 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:42:08,213 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:42:08.213716 (Duration: 0:00:03.446225) ---
2025-05-16 20:42:08,214 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:42:27,136 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:42:27,137 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:42:27,137 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:42:27,138 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:42:27.138797 ---
2025-05-16 20:42:27,139 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:42:27,449 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:42:27,450 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:42:27,450 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:42:27,454 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:42:27,455 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:42:27,873 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:42:28,612 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:42:28,741 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:42:28,791 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:42:28,799 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:42:28,983 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:42:29,327 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:42:29,379 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:42:31,394 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:42:31,395 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:42:31,397 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:42:32,360 - INFO - refresh_cache_script - No new user-specific update messages collected for mohamed.elsaadi in this run. No batch to cache.
2025-05-16 20:42:32,361 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:42:32,362 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:42:32,362 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:42:32,363 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:42:32.363311 (Duration: 0:00:05.224514) ---
2025-05-16 20:42:32,364 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:43:18,950 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:43:18,950 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:43:18,951 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:43:18,952 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:43:18.952599 ---
2025-05-16 20:43:18,953 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:43:19,157 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:43:19,158 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:43:19,158 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:43:19,162 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:43:19,163 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:43:19,673 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:43:20,269 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:43:20,270 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:43:20,338 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:43:20,361 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:43:20,471 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:43:20,577 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:43:20,761 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:43:20,803 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:43:20,826 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:43:20,827 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:43:21,569 - INFO - refresh_cache_script - No new user-specific update messages collected for mohamed.elsaadi in this run. No batch to cache.
2025-05-16 20:43:21,570 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:43:21,570 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:43:21,571 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:43:21,572 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:43:21.572237 (Duration: 0:00:02.619638) ---
2025-05-16 20:43:21,573 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:47:19,446 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:47:19,447 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:47:19,448 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:47:19,449 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:47:19.449301 ---
2025-05-16 20:47:19,450 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:47:19,662 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:47:19,663 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:47:19,664 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:47:19,667 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:47:19,668 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:47:20,147 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:47:20,937 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:47:20,993 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:47:21,067 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:47:21,068 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:47:21,161 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:47:21,168 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:47:21,276 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:47:21,599 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:47:21,602 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:47:21,604 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:47:22,021 - INFO - refresh_cache_script - Compare function for grades found 16 changes for mohamed.elsaadi.
2025-05-16 20:47:22,362 - INFO - refresh_cache_script - Collected 16 new user-specific update message(s) for mohamed.elsaadi in this run. Preparing batch.
2025-05-16 20:47:22,788 - INFO - refresh_cache_script - Successfully cached consolidated batch of 16 updates for mohamed.elsaadi. Total batches: 5.
2025-05-16 20:47:22,789 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:47:22,789 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:47:22,790 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:47:22,791 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:47:22.791156 (Duration: 0:00:03.341855) ---
2025-05-16 20:47:22,791 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:47:30,662 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:47:30,663 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:47:30,663 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:47:30,665 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:47:30.665167 ---
2025-05-16 20:47:30,665 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:47:30,872 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:47:30,873 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:47:30,874 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:47:30,877 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:47:30,878 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:47:31,414 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:47:31,969 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:47:31,972 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:47:31,980 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:47:32,112 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:47:32,139 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:47:32,144 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:47:32,438 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:47:32,578 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:47:32,616 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:47:32,618 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:47:33,032 - INFO - refresh_cache_script - Compare function for grades found 2 changes for mohamed.elsaadi.
2025-05-16 20:47:33,372 - INFO - refresh_cache_script - Collected 2 new user-specific update message(s) for mohamed.elsaadi in this run. Preparing batch.
2025-05-16 20:47:33,789 - INFO - refresh_cache_script - Successfully cached consolidated batch of 2 updates for mohamed.elsaadi. Total batches: 5.
2025-05-16 20:47:33,790 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:47:33,790 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:47:33,791 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:47:33,792 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:47:33.792025 (Duration: 0:00:03.126858) ---
2025-05-16 20:47:33,792 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:47:38,899 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:47:38,899 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:47:38,900 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:47:38,901 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:47:38.901518 ---
2025-05-16 20:47:38,902 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:47:39,109 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:47:39,110 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:47:39,111 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:47:39,114 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:47:39,115 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:47:39,602 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:47:40,042 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:47:40,230 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:47:40,232 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:47:40,384 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:47:40,389 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:47:40,455 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:47:40,596 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:47:41,090 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:47:42,981 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:47:42,983 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:47:43,400 - INFO - refresh_cache_script - Compare function for grades found 4 changes for mohamed.elsaadi.
2025-05-16 20:47:43,763 - INFO - refresh_cache_script - Collected 4 new user-specific update message(s) for mohamed.elsaadi in this run. Preparing batch.
2025-05-16 20:47:44,183 - INFO - refresh_cache_script - Successfully cached consolidated batch of 4 updates for mohamed.elsaadi. Total batches: 5.
2025-05-16 20:47:44,184 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:47:44,185 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:47:44,186 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:47:44,186 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:47:44.186817 (Duration: 0:00:05.285299) ---
2025-05-16 20:47:44,187 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:53:07,686 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:53:07,686 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:53:07,687 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:53:07,689 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:53:07.689089 ---
2025-05-16 20:53:07,689 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:53:07,899 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:53:07,899 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:53:07,900 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:53:07,904 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:53:07,905 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:53:08,384 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:53:08,913 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:53:08,915 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:53:08,961 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:53:09,230 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:53:09,503 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:53:09,637 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:53:09,849 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:53:09,935 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:53:10,267 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:53:10,268 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:53:10,694 - INFO - refresh_cache_script - Compare function for grades found 2 changes for mohamed.elsaadi.
2025-05-16 20:53:11,330 - INFO - refresh_cache_script - Collected 2 new user-specific update message(s) for mohamed.elsaadi in this run. Preparing batch.
2025-05-16 20:53:11,766 - INFO - refresh_cache_script - Successfully cached consolidated batch of 2 updates for mohamed.elsaadi. Total batches: 5.
2025-05-16 20:53:11,767 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:53:11,767 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:53:11,768 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:53:11,769 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:53:11.769002 (Duration: 0:00:04.079913) ---
2025-05-16 20:53:11,769 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:53:43,969 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:53:43,970 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:53:43,971 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:53:43,972 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:53:43.972291 ---
2025-05-16 20:53:43,973 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:53:44,176 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:53:44,177 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:53:44,177 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:53:44,181 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:53:44,182 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:53:44,637 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:53:45,394 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:53:45,405 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:53:45,474 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:53:45,488 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:53:45,705 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:53:45,840 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:53:46,330 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:53:48,190 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:53:48,191 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:53:48,192 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:53:48,597 - INFO - refresh_cache_script - Compare function for grades found 16 changes for mohamed.elsaadi.
2025-05-16 20:53:48,936 - INFO - refresh_cache_script - Collected 16 new user-specific update message(s) for mohamed.elsaadi in this run. Preparing batch.
2025-05-16 20:53:49,346 - INFO - refresh_cache_script - Successfully cached consolidated batch of 16 updates for mohamed.elsaadi. Total batches: 5.
2025-05-16 20:53:49,347 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:53:49,347 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:53:49,348 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:53:49,349 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:53:49.349191 (Duration: 0:00:05.376900) ---
2025-05-16 20:53:49,349 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:56:13,532 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:56:13,533 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:56:13,534 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:56:13,535 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:56:13.535175 ---
2025-05-16 20:56:13,535 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:56:13,838 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:56:13,839 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:56:13,839 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:56:13,843 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:56:13,844 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:56:14,236 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:56:14,955 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:56:15,029 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:56:15,091 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:56:15,318 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:56:15,419 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:56:15,456 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:56:15,457 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:56:15,644 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:56:15,863 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:56:15,865 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:56:16,470 - INFO - refresh_cache_script - Compare function for grades found 17 changes for mohamed.elsaadi.
2025-05-16 20:56:16,908 - INFO - refresh_cache_script - Collected 17 new user-specific update message(s) for mohamed.elsaadi in this run. Preparing batch.
2025-05-16 20:56:17,523 - INFO - refresh_cache_script - Successfully cached consolidated batch of 17 updates for mohamed.elsaadi. Total batches: 5.
2025-05-16 20:56:17,524 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:56:17,524 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:56:17,525 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:56:17,526 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:56:17.526144 (Duration: 0:00:03.990969) ---
2025-05-16 20:56:17,526 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:58:34,078 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:58:34,078 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:58:34,079 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:58:34,081 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:58:34.081035 ---
2025-05-16 20:58:34,081 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:58:34,375 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:58:34,376 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:58:34,377 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:58:34,381 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:58:34,381 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:58:34,815 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:58:35,492 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:58:35,493 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:58:35,580 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:58:35,825 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:58:35,903 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:58:36,364 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:58:36,589 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:58:36,600 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:58:38,548 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:58:38,550 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:58:39,136 - INFO - refresh_cache_script - Compare function for grades found 17 changes for mohamed.elsaadi.
2025-05-16 20:58:39,565 - INFO - refresh_cache_script - Collected 17 new user-specific update message(s) for mohamed.elsaadi in this run. Preparing batch.
2025-05-16 20:58:40,165 - INFO - refresh_cache_script - Successfully cached consolidated batch of 17 updates for mohamed.elsaadi. Total batches: 5.
2025-05-16 20:58:40,165 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:58:40,166 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:58:40,167 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:58:40,167 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:58:40.167843 (Duration: 0:00:06.086808) ---
2025-05-16 20:58:40,168 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 20:59:36,118 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 20:59:36,118 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 20:59:36,118 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 20:59:36,119 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T20:59:36.119922 ---
2025-05-16 20:59:36,120 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 20:59:36,327 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 20:59:36,328 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 20:59:36,328 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 20:59:36,331 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 20:59:36,333 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 20:59:36,842 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 20:59:37,544 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 20:59:37,637 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 20:59:37,642 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 20:59:37,708 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 20:59:37,753 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 20:59:38,214 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 20:59:38,298 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 20:59:38,309 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 20:59:40,505 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 20:59:40,506 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 20:59:40,924 - INFO - refresh_cache_script - Compare function for grades found 17 changes for mohamed.elsaadi.
2025-05-16 20:59:41,274 - INFO - refresh_cache_script - Collected 17 new user-specific update message(s) for mohamed.elsaadi in this run. Preparing batch.
2025-05-16 20:59:41,709 - INFO - refresh_cache_script - Successfully cached consolidated batch of 17 updates for mohamed.elsaadi. Total batches: 5.
2025-05-16 20:59:41,710 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 20:59:41,711 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 20:59:41,712 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 20:59:41,713 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T20:59:41.713044 (Duration: 0:00:05.593122) ---
2025-05-16 20:59:41,713 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 21:00:31,231 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 21:00:31,231 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 21:00:31,232 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 21:00:31,233 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T21:00:31.233612 ---
2025-05-16 21:00:31,234 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 21:00:31,446 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 21:00:31,447 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 21:00:31,447 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 21:00:31,451 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 21:00:31,452 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 21:00:32,142 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 21:00:32,558 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 21:00:32,560 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 21:00:32,797 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 21:00:33,206 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 21:00:33,253 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 21:00:33,445 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 21:00:33,563 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 21:00:33,941 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 21:00:36,342 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 21:00:36,343 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 21:00:36,958 - ERROR - utils.cache - [Cache] Error decoding JSON for key 'grades:mohamed.elsaadi': Extra data: line 1 column 7321 (char 7320). Data: b'{"midterm_results": {"General - Scientific Methods (A1) SM101": "50", "Engineering 2nd Semester - En'
2025-05-16 21:00:37,400 - INFO - refresh_cache_script - No new user-specific update messages collected for mohamed.elsaadi in this run. No batch to cache.
2025-05-16 21:00:37,401 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 21:00:37,402 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 21:00:37,402 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 21:00:37,403 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T21:00:37.403736 (Duration: 0:00:06.170124) ---
2025-05-16 21:00:37,404 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 21:01:54,588 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 21:01:54,589 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 21:01:54,589 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 21:01:54,590 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T21:01:54.590656 ---
2025-05-16 21:01:54,591 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 21:01:54,889 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 21:01:54,890 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 21:01:54,890 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 21:01:54,893 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 21:01:54,894 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 21:01:55,479 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 21:01:56,098 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 21:01:56,100 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 21:01:56,176 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 21:01:56,225 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 21:01:56,282 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 21:01:56,612 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 21:01:56,828 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 21:01:56,995 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 21:01:59,298 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 21:01:59,300 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 21:01:59,918 - DEBUG - utils.notifications_utils - DEBUG_GRADES User: mohamed.elsaadi, Course: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design, ItemKey: Quiz 2::Question1::0
2025-05-16 21:01:59,919 - DEBUG - utils.notifications_utils -   NewRaw: '/ 20', NewCleaned: '/ 20', IsNewPlaceholder: True
2025-05-16 21:01:59,919 - DEBUG - utils.notifications_utils -   Item NOT IN old_grades_map_by_item_key
2025-05-16 21:01:59,920 - DEBUG - utils.notifications_utils - DEBUG_GRADES User: mohamed.elsaadi, Course: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design, ItemKey: Tutorial 9::Question1::0
2025-05-16 21:01:59,921 - DEBUG - utils.notifications_utils -   NewRaw: '/ 10', NewCleaned: '/ 10', IsNewPlaceholder: True
2025-05-16 21:01:59,922 - DEBUG - utils.notifications_utils -   Item NOT IN old_grades_map_by_item_key
2025-05-16 21:01:59,922 - INFO - refresh_cache_script - Compare function for grades found 2 changes for mohamed.elsaadi.
2025-05-16 21:02:00,358 - INFO - refresh_cache_script - Collected 2 new user-specific update message(s) for mohamed.elsaadi in this run. Preparing batch.
2025-05-16 21:02:01,200 - INFO - refresh_cache_script - Successfully cached consolidated batch of 2 updates for mohamed.elsaadi. Total batches: 5.
2025-05-16 21:02:01,201 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 21:02:01,202 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 21:02:01,202 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 21:02:01,203 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T21:02:01.203671 (Duration: 0:00:06.613015) ---
2025-05-16 21:02:01,204 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 21:02:24,563 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 21:02:24,564 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 21:02:24,564 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 21:02:24,566 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T21:02:24.566184 ---
2025-05-16 21:02:24,566 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 21:02:24,782 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 21:02:24,782 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 21:02:24,783 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 21:02:24,787 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 21:02:24,787 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 21:02:25,233 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 21:02:25,891 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 21:02:26,039 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 21:02:26,041 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 21:02:26,046 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 21:02:26,132 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 21:02:26,369 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 21:02:26,390 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 21:02:26,532 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 21:02:26,764 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 21:02:26,765 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 21:02:27,188 - DEBUG - utils.notifications_utils - DEBUG_GRADES User: mohamed.elsaadi, Course: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design, ItemKey: Quiz 2::Question1::0
2025-05-16 21:02:27,189 - DEBUG - utils.notifications_utils -   NewRaw: '/ 20', NewCleaned: '/ 20', IsNewPlaceholder: True
2025-05-16 21:02:27,189 - DEBUG - utils.notifications_utils -   OldMappedGrade: '10 / 20', OldDispName: 'Quiz 2'
2025-05-16 21:02:27,190 - DEBUG - utils.notifications_utils - DEBUG_GRADES User: mohamed.elsaadi, Course: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design, ItemKey: Tutorial 9::Question1::0
2025-05-16 21:02:27,191 - DEBUG - utils.notifications_utils -   NewRaw: '/ 10', NewCleaned: '/ 10', IsNewPlaceholder: True
2025-05-16 21:02:27,192 - DEBUG - utils.notifications_utils -   OldMappedGrade: '10 / 10', OldDispName: 'Tutorial 9'
2025-05-16 21:02:27,192 - INFO - refresh_cache_script - Compare function for grades found 17 changes for mohamed.elsaadi.
2025-05-16 21:02:27,542 - INFO - refresh_cache_script - Collected 17 new user-specific update message(s) for mohamed.elsaadi in this run. Preparing batch.
2025-05-16 21:02:27,982 - INFO - refresh_cache_script - Successfully cached consolidated batch of 17 updates for mohamed.elsaadi. Total batches: 5.
2025-05-16 21:02:27,982 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 21:02:27,983 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 21:02:27,984 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 21:02:27,985 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T21:02:27.985073 (Duration: 0:00:03.418889) ---
2025-05-16 21:02:27,985 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-16 21:03:07,865 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-16 21:03:07,866 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-16 21:03:07,867 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-16 21:03:07,868 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-16T21:03:07.868547 ---
2025-05-16 21:03:07,869 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-16 21:03:08,084 - INFO - refresh_cache_script - Targeting refresh for single user: mohamed.elsaadi
2025-05-16 21:03:08,085 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-16 21:03:08,085 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_courses', 'grades']
2025-05-16 21:03:08,089 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-16 21:03:08,090 - INFO - scraping.grades - Starting grades scraping for mohamed.elsaadi from https://apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx
2025-05-16 21:03:08,713 - INFO - scraping.grades - Found 7 subjects. Fetching detailed grades...
2025-05-16 21:03:09,399 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - CSEN202 Introduction to Computer Programming
2025-05-16 21:03:09,502 - INFO - scraping.grades - Detailed grades table found but is empty.
2025-05-16 21:03:09,505 - INFO - scraping.grades - Successfully processed detailed grades task for: General - DE202 Basic German 2
2025-05-16 21:03:09,587 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ELCT201 Digital Logic Design
2025-05-16 21:03:09,669 - INFO - scraping.grades - Successfully processed detailed grades task for: General - SM101 Scientific Methods (A1)
2025-05-16 21:03:09,774 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-16 21:03:10,045 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - PHYS202 Physics II
2025-05-16 21:03:10,308 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design
2025-05-16 21:03:12,313 - INFO - scraping.grades - Successfully processed detailed grades task for: Engineering 2nd Semester - MATH203 Mathematics I
2025-05-16 21:03:12,314 - INFO - scraping.grades - Finished fetching detailed grades for mohamed.elsaadi.
2025-05-16 21:03:12,754 - DEBUG - utils.notifications_utils - DEBUG_GRADES User: mohamed.elsaadi, Course: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design, ItemKey: Quiz 2::Question1::0
2025-05-16 21:03:12,755 - DEBUG - utils.notifications_utils -   NewRaw: '/ 20', NewCleaned: '/ 20', IsNewPlaceholder: True
2025-05-16 21:03:12,756 - DEBUG - utils.notifications_utils -   OldMappedGrade: '10 / 20', OldDispName: 'Quiz 2'
2025-05-16 21:03:12,756 - DEBUG - utils.notifications_utils - DEBUG_GRADES User: mohamed.elsaadi, Course: Engineering 2nd Semester - ENGD301 Engineering Drawing & Design, ItemKey: Tutorial 9::Question1::0
2025-05-16 21:03:12,757 - DEBUG - utils.notifications_utils -   NewRaw: '/ 10', NewCleaned: '/ 10', IsNewPlaceholder: True
2025-05-16 21:03:12,758 - DEBUG - utils.notifications_utils -   OldMappedGrade: '10 / 10', OldDispName: 'Tutorial 9'
2025-05-16 21:03:12,758 - INFO - refresh_cache_script - Compare function for grades found 17 changes for mohamed.elsaadi.
2025-05-16 21:03:13,109 - INFO - refresh_cache_script - Collected 17 new user-specific update message(s) for mohamed.elsaadi in this run. Preparing batch.
2025-05-16 21:03:13,560 - INFO - refresh_cache_script - Successfully cached consolidated batch of 17 updates for mohamed.elsaadi. Total batches: 5.
2025-05-16 21:03:13,561 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-16 21:03:13,561 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-16 21:03:13,562 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_courses: updated; grades: updated
2025-05-16 21:03:13,563 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-16T21:03:13.563070 (Duration: 0:00:05.694523) ---
2025-05-16 21:03:13,563 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=2, Skipped=0, Failed=0
2025-05-17 19:43:45,757 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-17 19:43:45,758 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-17 19:43:45,759 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-17 19:43:45,761 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-17T19:43:45.761761 ---
2025-05-17 19:43:45,762 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-17 19:43:45,967 - INFO - refresh_cache_script - Refreshing section 4 (cms_content) for 6 users.
2025-05-17 19:43:45,968 - INFO - refresh_cache_script - Starting processing for user: malak.mohamedelkady
2025-05-17 19:43:45,969 - INFO - refresh_cache_script - Processing user: malak.mohamedelkady for data types: ['cms_content']
2025-05-17 19:43:45,970 - INFO - refresh_cache_script - Initiating deep CMS content refresh for user: malak.mohamedelkady
2025-05-17 19:43:45,973 - INFO - scraping.cms - Fetching CMS course list for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-17 19:43:45,973 - INFO - refresh_cache_script - Starting processing for user: malek.amer
2025-05-17 19:43:45,975 - INFO - refresh_cache_script - Processing user: malek.amer for data types: ['cms_content']
2025-05-17 19:43:45,976 - INFO - refresh_cache_script - Initiating deep CMS content refresh for user: malek.amer
2025-05-17 19:43:45,977 - INFO - scraping.cms - Fetching CMS course list for malek.amer from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-17 19:43:45,978 - INFO - refresh_cache_script - Starting processing for user: ahmed.abd-elhamid
2025-05-17 19:43:45,980 - INFO - refresh_cache_script - Processing user: ahmed.abd-elhamid for data types: ['cms_content']
2025-05-17 19:43:45,981 - INFO - refresh_cache_script - Initiating deep CMS content refresh for user: ahmed.abd-elhamid
2025-05-17 19:43:45,982 - INFO - scraping.cms - Fetching CMS course list for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-17 19:43:45,982 - INFO - refresh_cache_script - Starting processing for user: seif.elkady
2025-05-17 19:43:45,984 - INFO - refresh_cache_script - Processing user: seif.elkady for data types: ['cms_content']
2025-05-17 19:43:45,985 - INFO - refresh_cache_script - Initiating deep CMS content refresh for user: seif.elkady
2025-05-17 19:43:45,986 - INFO - scraping.cms - Fetching CMS course list for seif.elkady from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-17 19:43:45,986 - INFO - refresh_cache_script - Starting processing for user: abobakr.bedda
2025-05-17 19:43:45,988 - INFO - refresh_cache_script - Processing user: abobakr.bedda for data types: ['cms_content']
2025-05-17 19:43:45,989 - INFO - refresh_cache_script - Initiating deep CMS content refresh for user: abobakr.bedda
2025-05-17 19:43:45,990 - INFO - scraping.cms - Fetching CMS course list for abobakr.bedda from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-17 19:43:45,991 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-17 19:43:45,993 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_content']
2025-05-17 19:43:45,993 - INFO - refresh_cache_script - Initiating deep CMS content refresh for user: mohamed.elsaadi
2025-05-17 19:43:45,995 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-17 19:43:48,133 - INFO - scraping.cms - Successfully scraped 7 courses for malek.amer.
2025-05-17 19:43:48,135 - INFO - refresh_cache_script - Found 7 courses for malek.amer. Creating refresh tasks.
2025-05-17 19:43:48,136 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-17 19:43:48,136 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:43:48,138 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:43:48,141 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:43:48,142 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:43:48,143 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:43:48,144 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:43:48,147 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-05-17 19:43:48,149 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-05-17 19:43:48,151 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:43:48,153 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:43:48,154 - INFO - refresh_cache_script - Found 8 courses for mohamed.elsaadi. Creating refresh tasks.
2025-05-17 19:43:48,158 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:43:48,159 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:43:48,162 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:43:48,164 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:43:48,166 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:43:48,167 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:43:48,169 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:43:48,170 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:43:48,173 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:43:48,175 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:43:48,435 - INFO - scraping.cms - Successfully scraped 8 courses for abobakr.bedda.
2025-05-17 19:43:48,436 - INFO - refresh_cache_script - Found 8 courses for abobakr.bedda. Creating refresh tasks.
2025-05-17 19:43:48,437 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:43:48,437 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:43:48,438 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:43:48,441 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:43:48,445 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:43:48,448 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:43:48,449 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:43:48,450 - INFO - scraping.cms - Successfully scraped 8 courses for malak.mohamedelkady.
2025-05-17 19:43:48,452 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:43:48,454 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:43:48,456 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:43:48,458 - INFO - refresh_cache_script - Found 8 courses for malak.mohamedelkady. Creating refresh tasks.
2025-05-17 19:43:48,467 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-05-17 19:43:48,468 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-05-17 19:43:48,470 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-05-17 19:43:48,472 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-05-17 19:43:48,482 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-05-17 19:43:48,483 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-05-17 19:43:48,484 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-05-17 19:43:48,485 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-05-17 19:43:48,490 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-05-17 19:43:48,491 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-05-17 19:43:48,639 - INFO - scraping.cms - Successfully scraped 8 courses for ahmed.abd-elhamid.
2025-05-17 19:43:48,641 - INFO - refresh_cache_script - Found 8 courses for ahmed.abd-elhamid. Creating refresh tasks.
2025-05-17 19:43:48,642 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:43:48,642 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:43:48,645 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:43:48,646 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:43:48,648 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:43:48,649 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:43:48,654 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:43:48,656 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:43:48,657 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:43:48,669 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:43:48,847 - INFO - scraping.cms - Successfully scraped 8 courses for seif.elkady.
2025-05-17 19:43:48,849 - INFO - refresh_cache_script - Found 8 courses for seif.elkady. Creating refresh tasks.
2025-05-17 19:43:48,850 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:43:48,853 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:43:48,856 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:43:48,857 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:43:48,858 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:43:48,863 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:43:48,870 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:43:48,873 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:43:48,875 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:43:48,876 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:43:49,928 - WARNING - scraping.cms - No week sections found (selector '.weeksdata').
2025-05-17 19:43:49,928 - WARNING - scraping.cms - No week sections found (selector '.weeksdata').
2025-05-17 19:43:49,929 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 0 weeks.
2025-05-17 19:43:49,929 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 0 weeks.
2025-05-17 19:43:51,504 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:43:51,565 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:43:51,655 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65. Found 8 weeks.
2025-05-17 19:43:51,657 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-05-17 19:43:51,821 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 11 weeks.
2025-05-17 19:43:51,821 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:51,986 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-05-17 19:43:51,988 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-05-17 19:43:51,990 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-05-17 19:43:51,990 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-05-17 19:43:51,991 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-05-17 19:43:51,992 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-05-17 19:43:51,993 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-05-17 19:43:51,994 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:43:51,996 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:52,017 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-05-17 19:43:52,143 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:52,148 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:52,220 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-05-17 19:43:52,221 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-05-17 19:43:52,222 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-05-17 19:43:52,223 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-05-17 19:43:52,223 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-05-17 19:43:52,224 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-05-17 19:43:52,225 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-05-17 19:43:52,227 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:43:52,229 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:52,235 - WARNING - scraping.cms - Course announcement section not found on https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65 for malek.amer.
2025-05-17 19:43:52,238 - WARNING - scraping.cms - No week sections found (selector '.weeksdata').
2025-05-17 19:43:52,239 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 0 weeks.
2025-05-17 19:43:52,244 - WARNING - scraping.cms - Course announcement section not found on https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65 for malek.amer.
2025-05-17 19:43:52,286 - WARNING - scraping.cms - Course announcement section not found on https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65 for mohamed.elsaadi.
2025-05-17 19:43:52,301 - WARNING - scraping.cms - No week sections found (selector '.weeksdata').
2025-05-17 19:43:52,302 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 0 weeks.
2025-05-17 19:43:52,422 - WARNING - scraping.cms - Course announcement section not found on https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65 for abobakr.bedda.
2025-05-17 19:43:52,482 - WARNING - scraping.cms - No week sections found (selector '.weeksdata').
2025-05-17 19:43:52,483 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65. Found 0 weeks.
2025-05-17 19:43:52,494 - WARNING - scraping.cms - No week sections found (selector '.weeksdata').
2025-05-17 19:43:52,495 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 0 weeks.
2025-05-17 19:43:52,513 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:43:52,515 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:43:52,530 - WARNING - scraping.cms - Course announcement section not found on https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65 for mohamed.elsaadi.
2025-05-17 19:43:52,536 - WARNING - scraping.cms - Course announcement section not found on https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65 for mohamed.elsaadi.
2025-05-17 19:43:52,578 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-05-17 19:43:52,649 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:43:52,651 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:52,721 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:43:52,722 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:43:52,755 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:74760149fa9e6db7299f93ae95bd5a99 with expiry 3600 seconds
2025-05-17 19:43:52,756 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malek.amer - (|EDPT201|) Production Technology (432)
2025-05-17 19:43:52,758 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:43:52,759 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:43:52,833 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:43:52,834 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:52,868 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:43:52,951 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:53,083 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 11 weeks.
2025-05-17 19:43:53,089 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:43:53,112 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:53,133 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-05-17 19:43:53,136 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-05-17 19:43:53,137 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-05-17 19:43:53,138 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:53,215 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:53,270 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-05-17 19:43:53,271 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-05-17 19:43:53,273 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-05-17 19:43:53,274 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:53,424 - WARNING - scraping.cms - No week sections found (selector '.weeksdata').
2025-05-17 19:43:53,425 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 0 weeks.
2025-05-17 19:43:53,467 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:53,568 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65. Found 11 weeks.
2025-05-17 19:43:53,663 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:53,799 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:43:53,803 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-05-17 19:43:53,940 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:54,037 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:43:54,119 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-05-17 19:43:54,133 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-17 19:43:54,135 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:54,336 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:43:54,343 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:4462b27cf56e0798d09f300eeeb65adb with expiry 3600 seconds
2025-05-17 19:43:54,344 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-17 19:43:54,345 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malek.amer - (|PHYS202|) Physics II (450)
2025-05-17 19:43:54,346 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:54,433 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:43:54,454 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65. Found 12 weeks.
2025-05-17 19:43:54,661 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-17 19:43:54,663 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:54,722 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:55,091 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-05-17 19:43:55,092 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-05-17 19:43:55,093 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-05-17 19:43:55,094 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-05-17 19:43:55,095 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-05-17 19:43:55,096 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-05-17 19:43:55,097 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-05-17 19:43:55,098 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:43:55,100 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:55,319 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:55,335 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:55,402 - INFO - scraping.cms - VOD '2 - Blockchain Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b3790955-857d-ac72-102a-6566cc43a321' for access URL.
2025-05-17 19:43:55,403 - INFO - scraping.cms - VOD '3 - Tutorial 5 VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-58216c6b-b89b-8fcd-d41f-d8c99c564067' for access URL.
2025-05-17 19:43:55,407 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65. Found 11 weeks.
2025-05-17 19:43:55,430 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:55,437 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-05-17 19:43:55,438 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-05-17 19:43:55,439 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-05-17 19:43:55,440 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-05-17 19:43:55,440 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-05-17 19:43:55,441 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-05-17 19:43:55,442 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-05-17 19:43:55,445 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:43:55,447 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:55,504 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:55,615 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 11 weeks.
2025-05-17 19:43:55,717 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 10 weeks.
2025-05-17 19:43:55,780 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:55,909 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:43:55,911 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:56,172 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:43:56,193 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:43:56,194 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:56,213 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 12 weeks.
2025-05-17 19:43:56,214 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:56,232 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:43:56,266 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:43:56,351 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:56,435 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65. Found 11 weeks.
2025-05-17 19:43:56,486 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:43:56,529 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-05-17 19:43:56,530 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-05-17 19:43:56,532 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-05-17 19:43:56,533 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:56,536 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:9498fb390f46840b3f1f0c28cf2518fc with expiry 3600 seconds
2025-05-17 19:43:56,536 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for abobakr.bedda - (|MATH203|) Mathematics I (17)
2025-05-17 19:43:56,542 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 12 weeks.
2025-05-17 19:43:56,543 - WARNING - refresh_cache_script - Announcement scraping reported error for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65: Announcement section not found
2025-05-17 19:43:56,544 - WARNING - refresh_cache_script - Skipped CMS content cache update for abobakr.bedda - (|PHYS202|) Physics II (450) - no data assembled.
2025-05-17 19:43:56,546 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:43:56,546 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:43:56,547 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:43:56,549 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:43:56,550 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:43:56,552 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:43:56,661 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:43:56,697 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-05-17 19:43:56,698 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-05-17 19:43:56,700 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-05-17 19:43:56,701 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:56,714 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65. Found 17 weeks.
2025-05-17 19:43:56,729 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:43:56,743 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:56,767 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-05-17 19:43:56,806 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 11 weeks.
2025-05-17 19:43:56,814 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 10 weeks.
2025-05-17 19:43:56,855 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:56,883 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-05-17 19:43:56,918 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 10 weeks.
2025-05-17 19:43:56,923 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-05-17 19:43:56,973 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:57,040 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-05-17 19:43:57,042 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-05-17 19:43:57,043 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-05-17 19:43:57,044 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-05-17 19:43:57,045 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-05-17 19:43:57,046 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-05-17 19:43:57,047 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-05-17 19:43:57,048 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:43:57,049 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:57,076 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-05-17 19:43:57,149 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-05-17 19:43:57,353 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:57,417 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:43:57,419 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:57,455 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-05-17 19:43:57,492 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:57,494 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:43:57,509 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:43:57,690 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:57,775 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-05-17 19:43:57,776 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-05-17 19:43:57,777 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-05-17 19:43:57,779 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:57,789 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:57,870 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:57,938 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:65b03f76c617f30ea9ca30bcd51951a9 with expiry 3600 seconds
2025-05-17 19:43:57,939 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malek.amer - (|MATH203|) Mathematics I (17)
2025-05-17 19:43:57,950 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 12 weeks.
2025-05-17 19:43:58,039 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:58,098 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-05-17 19:43:58,120 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:58,419 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:43:58,548 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 10 weeks.
2025-05-17 19:43:58,800 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:58,991 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:59,114 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:59,283 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:5056725a2e1d095a8b1519daa2e9895e with expiry 3600 seconds
2025-05-17 19:43:59,284 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|BINF405|) Information and Communication Architecture II (486)
2025-05-17 19:43:59,353 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:59,473 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:59,579 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:59,671 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:43:59,936 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:43:59,998 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:00,083 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65. Found 17 weeks.
2025-05-17 19:44:00,212 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:00,411 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:00,476 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:00,573 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:******************************** with expiry 3600 seconds
2025-05-17 19:44:00,574 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - (|SM101|) Scientific Methods (A1) (16)
2025-05-17 19:44:00,575 - WARNING - refresh_cache_script - Announcement scraping reported error for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65: Announcement section not found
2025-05-17 19:44:00,740 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:01,207 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:01,515 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:01,533 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:01,534 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:01,734 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:bf3a6dfb2a4c8c41858dc8db75d5c026 with expiry 3600 seconds
2025-05-17 19:44:01,735 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malek.amer - (|SM101|) Scientific Methods (A1) (16)
2025-05-17 19:44:01,841 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:01,861 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:01,872 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:02,186 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:02,207 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:02,421 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:02,444 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:02,504 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-17 19:44:02,505 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:02,693 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:02,782 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:1ff8172699c20c7d216ba3df453f68ad with expiry 3600 seconds
2025-05-17 19:44:02,783 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|BINF406|) Digital Transformation (2708)
2025-05-17 19:44:02,819 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-05-17 19:44:03,012 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-17 19:44:03,014 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:03,056 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-17 19:44:03,058 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:03,329 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-05-17 19:44:03,399 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-05-17 19:44:03,925 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:8dbe3d6d795d967a224c77531426cee5 with expiry 3600 seconds
2025-05-17 19:44:03,926 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - (|PHYS202|) Physics II (450)
2025-05-17 19:44:03,927 - WARNING - refresh_cache_script - Announcement scraping reported error for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65: Announcement section not found
2025-05-17 19:44:04,945 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:45438db64e5444f582aa00b0bb3daa33 with expiry 3600 seconds
2025-05-17 19:44:04,946 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - (|CSEN202|) Introduction to Computer Programming (19)
2025-05-17 19:44:06,239 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:cc237221e139fa9410b370a25ff75444 with expiry 3600 seconds
2025-05-17 19:44:06,240 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - (|MATH203|) Mathematics I (17)
2025-05-17 19:44:07,433 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:e980688428bad3a4e5d5de5383c83946 with expiry 3600 seconds
2025-05-17 19:44:07,434 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - (|CSEN202|) Introduction to Computer Programming (19)
2025-05-17 19:44:07,436 - WARNING - refresh_cache_script - Announcement scraping reported error for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65: Announcement section not found
2025-05-17 19:44:08,852 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:f97693fa9ffca93f43c8f520cbf667fe with expiry 3600 seconds
2025-05-17 19:44:08,853 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malek.amer - (|ELCT201|) Digital Logic Design (79)
2025-05-17 19:44:08,855 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-05-17 19:44:08,856 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-05-17 19:44:08,858 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:44:08,860 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:44:08,861 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-05-17 19:44:08,862 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-05-17 19:44:08,864 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:44:08,866 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:44:08,868 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:44:08,869 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:44:08,870 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:44:08,873 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:44:08,875 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:44:08,877 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:44:10,325 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:e0724bb0d12f6e68506d5000018dbd47 with expiry 3600 seconds
2025-05-17 19:44:10,326 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - (|ELCT201|) Digital Logic Design (79)
2025-05-17 19:44:11,022 - INFO - scraping.cms - VOD '2 - RPW-  Introduction of the literature review (VoD)' ID '150675_f_1056943' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:11,036 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-05-17 19:44:11,470 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:4d7ae447c4fe66c338e1951549466285 with expiry 3600 seconds
2025-05-17 19:44:11,472 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - (|ENGD301|) Engineering Drawing & Design (49)
2025-05-17 19:44:11,574 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:11,613 - INFO - scraping.cms - VOD '4 - RPW- Literature Review (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-51ed902d-c21f-51d3-1e23-de32b20bb82d' for access URL.
2025-05-17 19:44:11,614 - INFO - scraping.cms - VOD '6 - RPW- Methodology Section (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-607130fb-d676-62ac-7bfb-8e2cd817db36' for access URL.
2025-05-17 19:44:11,615 - INFO - scraping.cms - VOD '2 - RPW- Article Review (VoD)' ID '150675_f_1056934' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:11,629 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:44:11,903 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:12,182 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:12,262 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:12,365 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:44:12,427 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:44:12,534 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:12,540 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 12 weeks.
2025-05-17 19:44:12,694 - INFO - scraping.cms - VOD '2 - RPW Narrowing Down Steps (Scopus Database) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-bb767829-e552-91a0-eb6a-990d36e54ed2' for access URL.
2025-05-17 19:44:12,695 - INFO - scraping.cms - VOD '4 - RPW Narrowing Down Steps (EBSCOhost Database) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-906e2a09-9ac9-17b7-73d2-947aca7308ee' for access URL.
2025-05-17 19:44:12,696 - INFO - scraping.cms - VOD '4 - RPW- Research Terminology (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-d8fa6938-9caa-7132-215a-425f77ef607e' for access URL.
2025-05-17 19:44:12,698 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65. Found 5 weeks.
2025-05-17 19:44:12,730 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:0273cfb9d74dacfa10a29ddad35614a9 with expiry 3600 seconds
2025-05-17 19:44:12,731 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - (|PHYS202|) Physics II (450)
2025-05-17 19:44:12,988 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:13,289 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 13 weeks.
2025-05-17 19:44:13,532 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 13 weeks.
2025-05-17 19:44:13,582 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-17 19:44:13,583 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:13,874 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:818ee80513a8c43769d64244cd74d46a with expiry 3600 seconds
2025-05-17 19:44:13,875 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|HROB203|) Human Resources Management for BI (2488)
2025-05-17 19:44:13,920 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:14,526 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:14,831 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:44:15,022 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:f8e39e2adb24854f36d9a72a3a187964 with expiry 3600 seconds
2025-05-17 19:44:15,023 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - (|PHYS202|) Physics II (450)
2025-05-17 19:44:15,078 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:15,377 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:44:15,638 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:16,041 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65. Found 17 weeks.
2025-05-17 19:44:16,332 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:16,378 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:7d03f64d386af7cd72d63242d2c96bbd with expiry 3600 seconds
2025-05-17 19:44:16,379 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - (|CSEN202|) Introduction to Computer Programming (19)
2025-05-17 19:44:16,965 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:17,322 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:17,638 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:17,663 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:f6b71efcc7f9320b9f19caac58cb9b80 with expiry 3600 seconds
2025-05-17 19:44:17,664 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - (|SM101|) Scientific Methods (A1) (16)
2025-05-17 19:44:18,142 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:18,710 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:18,854 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-05-17 19:44:18,976 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:a3c6b37335fdd765767dd80df3cf895c with expiry 3600 seconds
2025-05-17 19:44:18,977 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malek.amer - (|CSEN202|) Introduction to Computer Programming (19)
2025-05-17 19:44:19,279 - INFO - scraping.cms - VOD '1 - Revision Part I (VoD)' ID '150675_f_1123696' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:19,372 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:19,603 - INFO - scraping.cms - VOD '1 - Lektion 24 Video 1 (VoD)' ID '150675_f_1073495' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:19,947 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:20,006 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:9fa67215c085dc81283eafaa5bbb4610 with expiry 3600 seconds
2025-05-17 19:44:20,007 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|CSEN401|) Computer Programming Lab (402)
2025-05-17 19:44:20,120 - INFO - scraping.cms - VOD '2 - Lektion 24 Video 1 Komplett (VoD)' ID '150675_f_1073500' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:20,439 - INFO - scraping.cms - VOD '3 - Lektion 24 Video 2 Komplett (VoD)' ID '150675_f_1073502' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:20,489 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:21,006 - INFO - scraping.cms - VOD '1 - Lektion 23 Video 1 (VoD)' ID '150675_f_1073450' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:21,071 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-17 19:44:21,073 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:21,296 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:bc53a4607c4dae46535866aa2fcd7676 with expiry 3600 seconds
2025-05-17 19:44:21,297 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - (|SM101|) Scientific Methods (A1) (16)
2025-05-17 19:44:21,566 - INFO - scraping.cms - VOD '2 - Lektion 23 Video 2 (VoD)' ID '150675_f_1073453' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:21,630 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-05-17 19:44:22,336 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:63af6194da837cf58d78115f83ccf5c9 with expiry 3600 seconds
2025-05-17 19:44:22,337 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|CSEN404|) Introduction to Networks (510)
2025-05-17 19:44:22,405 - INFO - scraping.cms - VOD '3 - Lektion 23 Video 3 (VoD)' ID '150675_f_1073456' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:22,959 - INFO - scraping.cms - VOD '4 - Lektion 23 Video 1 Komplett (VoD)' ID '150675_f_1073474' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:23,217 - INFO - scraping.cms - VOD '5 - Lektion 23 Video 2 Komplett (VoD)' ID '150675_f_1073481' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:23,371 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:943a7189d2212dfcf05e31a9d6ee610c with expiry 3600 seconds
2025-05-17 19:44:23,372 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malek.amer - Engineering - 2nd Orientation course
2025-05-17 19:44:23,726 - INFO - scraping.cms - VOD '6 - Lektion 23 Video 3 Komplett (VoD)' ID '150675_f_1073486' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:24,002 - INFO - scraping.cms - VOD '7 - Lektion 23 Video 4 Komplett (VoD)' ID '150675_f_1073485' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:24,932 - INFO - scraping.cms - VOD '1 - Lektion 22 Video 1 (VoD)' ID '150675_f_1073442' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:24,958 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:e70db9b896c65c16d0285ef5fa22aa5d with expiry 3600 seconds
2025-05-17 19:44:24,959 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for abobakr.bedda - (|ELCT201|) Digital Logic Design (79)
2025-05-17 19:44:24,960 - WARNING - refresh_cache_script - Announcement scraping reported error for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65: Announcement section not found
2025-05-17 19:44:25,814 - INFO - scraping.cms - VOD '2 - Lektion 22 Video 2 (VoD)' ID '150675_f_1073444' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:26,114 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:008ad4cc6e77b619200be2e49c404190 with expiry 3600 seconds
2025-05-17 19:44:26,115 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - (|ELCT201|) Digital Logic Design (79)
2025-05-17 19:44:26,578 - INFO - scraping.cms - VOD '3 - Lektion 22 complete video 1 (VoD)' ID '150675_f_1073448' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:26,904 - INFO - scraping.cms - VOD '4 - Lektion 22 complete video 2 (VoD)' ID '150675_f_1101615' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:27,195 - INFO - scraping.cms - VOD '1 - Unit 21 Part 1 (VoD)' ID '150675_f_1063834' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:27,271 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:6d4b66a6639821a41615818b09fae811 with expiry 3600 seconds
2025-05-17 19:44:27,272 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for abobakr.bedda - (|SM101|) Scientific Methods (A1) (16)
2025-05-17 19:44:28,171 - INFO - scraping.cms - VOD '2 - Unit 21 Part 2 (VoD)' ID '150675_f_1067572' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:28,417 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:e715d21507618ab36722c375ea8b23e3 with expiry 3600 seconds
2025-05-17 19:44:28,418 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for abobakr.bedda - (|CSEN202|) Introduction to Computer Programming (19)
2025-05-17 19:44:28,469 - INFO - scraping.cms - VOD '3 - Unit 21 (Complete Unit: Part 1) (VoD)' ID '150675_f_1094812' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:28,997 - INFO - scraping.cms - VOD '4 - Unit 21 (Complete Unit: Part 2) (VoD)' ID '150675_f_1094820' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:29,554 - INFO - scraping.cms - VOD '5 - Unit 21 (Complete Unit: Part 3) (VoD)' ID '150675_f_1094828' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:29,589 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:57ef0654c365fb79601466bd6aa9e55b with expiry 3600 seconds
2025-05-17 19:44:29,590 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for abobakr.bedda - (|ENGD301|) Engineering Drawing & Design (49)
2025-05-17 19:44:29,591 - WARNING - refresh_cache_script - Announcement scraping reported error for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65: Announcement section not found
2025-05-17 19:44:30,066 - INFO - scraping.cms - VOD '1 - Unit 20 (VoD)' ID '150675_f_1063828' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:30,392 - INFO - scraping.cms - VOD '2 - Unit 20 (Complete Unit) (VoD)' ID '150675_f_1092097' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:30,622 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:2040fe230931ce0bc66083b58bc494f5 with expiry 3600 seconds
2025-05-17 19:44:30,623 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - (|DE202|) Basic German 2 (33)
2025-05-17 19:44:30,719 - INFO - scraping.cms - VOD '3 - K19 - V1 (VoD)' ID '150675_f_1062891' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:31,648 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:e6b2b8002d98e046cb5176855f29605a with expiry 3600 seconds
2025-05-17 19:44:31,649 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - (|DE202|) Basic German 2 (33)
2025-05-17 19:44:31,738 - INFO - scraping.cms - VOD '4 - Unit 19 Complete Unit (Part1) (VoD)' ID '150675_f_1066246' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:32,061 - INFO - scraping.cms - VOD '5 - Unit 19 Complete Unit (Part 2) (VoD)' ID '150675_f_1066249' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:32,566 - INFO - scraping.cms - VOD '1 - Revision 1 (VoD)' ID '150675_f_1124540' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:32,680 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:8b3eb88d297eef9f6b09e272cc8a10f2 with expiry 3600 seconds
2025-05-17 19:44:32,681 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for abobakr.bedda - (|DE202|) Basic German 2 (33)
2025-05-17 19:44:32,683 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:44:32,684 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:44:32,685 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:44:32,687 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:44:32,688 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-05-17 19:44:32,690 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-05-17 19:44:32,691 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:44:32,693 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:44:32,694 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:44:32,697 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:44:32,700 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:44:32,702 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:44:33,121 - INFO - scraping.cms - VOD '2 - Revision 2 (VoD)' ID '150675_f_1124541' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:33,337 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-05-17 19:44:33,438 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:44:33,440 - INFO - scraping.cms - VOD '4 - Model test DE3 AUDIO (VoD)' ID '150675_f_1124235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:33,457 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-05-17 19:44:33,480 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-05-17 19:44:33,565 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:44:33,575 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-05-17 19:44:33,628 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:44:33,651 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:44:34,023 - INFO - scraping.cms - VOD '1 - U18-V1 Blended (VoD)' ID '150675_f_1090464' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:34,178 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:72d50b4aae2d29754439a6f472414afb with expiry 3600 seconds
2025-05-17 19:44:34,179 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - (|ELCT201|) Digital Logic Design (79)
2025-05-17 19:44:34,982 - INFO - scraping.cms - VOD '2 - U18-V2 Blended (VoD)' ID '150675_f_1090082' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:35,257 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:991454acfa6066cb44b51e7b641a315d with expiry 3600 seconds
2025-05-17 19:44:35,259 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|RPW401|) Research Paper Writing (A2) (34)
2025-05-17 19:44:35,262 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:44:35,266 - INFO - scraping.cms - VOD '4 - Final Revision - Part 1 (VoD)' ID '150675_f_1067515' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:35,370 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65. Found 17 weeks.
2025-05-17 19:44:35,594 - INFO - scraping.cms - VOD '3 - U18-V1 Complete (VoD)' ID '150675_f_1090084' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:35,834 - INFO - scraping.cms - VOD '5 - Final Revision: Part-2 (VoD)' ID '150675_f_1067508' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:36,553 - INFO - scraping.cms - VOD '4 - U18-V2 Complete (VoD)' ID '150675_f_1090083' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:36,555 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:2a5b20f14dad6774d3cf5929d32e776a with expiry 3600 seconds
2025-05-17 19:44:36,556 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - (|MATH203|) Mathematics I (17)
2025-05-17 19:44:36,844 - INFO - scraping.cms - VOD '6 - Final Revision: Part-3 (VoD)' ID '150675_f_1067512' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:37,258 - INFO - scraping.cms - VOD '1 - U17- V1 Blended (VoD)' ID '150675_f_1090058' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:37,306 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-05-17 19:44:37,761 - INFO - scraping.cms - VOD '3 - Tutorial 11 (ws 11) (VoD)' ID '150675_f_1069055' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:37,861 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:bce5e54a17608dbc809d36caec90886f with expiry 3600 seconds
2025-05-17 19:44:37,862 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - (|MATH203|) Mathematics I (17)
2025-05-17 19:44:38,091 - INFO - scraping.cms - VOD '3 - Tutorial 10 (ws 10) (VoD)' ID '150675_f_1069054' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:38,199 - INFO - scraping.cms - VOD '2 - U17-V2 Blended (VoD)' ID '150675_f_1090062' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:38,684 - INFO - scraping.cms - VOD '3 - Tutorial 9 (ws 9) (VoD)' ID '150675_f_1069048' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:38,886 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:835c207cbf8ea3f369d1ef2b4518231e with expiry 3600 seconds
2025-05-17 19:44:38,887 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - (|ENGD301|) Engineering Drawing & Design (49)
2025-05-17 19:44:39,216 - INFO - scraping.cms - VOD '3 - U17-V3 Blended (VoD)' ID '150675_f_1090064' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:39,583 - INFO - scraping.cms - VOD '3 - Tutorial 8 (ws 8) (VoD)' ID '150675_f_1069045' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:39,871 - INFO - scraping.cms - VOD '4 - U17 - V1 Complete (VoD)' ID '150675_f_1090063' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:39,912 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:d3acd0d76918ba01725396c4fd0e176b with expiry 3600 seconds
2025-05-17 19:44:39,913 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - (|DE202|) Basic German 2 (33)
2025-05-17 19:44:39,914 - INFO - refresh_cache_script - CMS Content refresh summary for malek.amer: updated=7, skipped=0, failed=0
2025-05-17 19:44:39,915 - INFO - refresh_cache_script - Finished processing for user: malek.amer
2025-05-17 19:44:40,122 - INFO - scraping.cms - VOD '5 - U17 - V2 Complete (VoD)' ID '150675_f_1090067' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:40,218 - INFO - scraping.cms - VOD '3 - Tutorial 7 (ws 7) (VoD)' ID '150675_f_1069042' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:40,678 - INFO - scraping.cms - VOD '6 - U17-V3 Complete (VoD)' ID '150675_f_1090071' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:40,750 - INFO - scraping.cms - VOD '3 - Tutorial 6 (ws 6) (VoD)' ID '150675_f_1069039' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:40,919 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:b2ed3a725884d8c0eab875a68dbf6ffe with expiry 3600 seconds
2025-05-17 19:44:40,920 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - Engineering - 2nd Orientation course
2025-05-17 19:44:41,082 - INFO - scraping.cms - VOD '4 - Midterm Revision (VoD)' ID '150675_f_1071230' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:41,254 - INFO - scraping.cms - VOD '1 - U16- V1 Blended (VoD)' ID '150675_f_1090053' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:41,953 - INFO - scraping.cms - VOD '3 - Tutorial 5 (ws 5) (VoD)' ID '150675_f_1071247' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:41,956 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:3de3baf04b6cc6442380c22fb0dd1329 with expiry 3600 seconds
2025-05-17 19:44:41,957 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - Engineering - 2nd Orientation course
2025-05-17 19:44:42,288 - INFO - scraping.cms - VOD '2 - U16- V1 complete (VoD)' ID '150675_f_1090057' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:42,848 - INFO - scraping.cms - VOD '3 - Tutorial 4 (ws 4) (VoD)' ID '150675_f_1067374' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:42,984 - INFO - scraping.cms - VOD '3 - U16-V2 complete (VoD)' ID '150675_f_1090462' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:43,185 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:76346a5a03930d128cfaaad5683d2c22 with expiry 3600 seconds
2025-05-17 19:44:43,186 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - Engineering - 2nd Orientation course
2025-05-17 19:44:43,742 - INFO - scraping.cms - VOD '1 - U13-complete Video1 (VoD)' ID '150675_f_1065580' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:43,947 - INFO - scraping.cms - VOD '3 - Tutorial 3 (ws 3) (VoD)' ID '150675_f_1066977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:44,355 - INFO - scraping.cms - VOD '2 - U13- complete Video2 (VoD)' ID '150675_f_1065582' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:44,356 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:026f8dd3a7fdf3cffe83226f2147261d with expiry 3600 seconds
2025-05-17 19:44:44,358 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for abobakr.bedda - Engineering - 2nd Orientation course
2025-05-17 19:44:44,554 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65. Found 16 weeks.
2025-05-17 19:44:44,903 - INFO - scraping.cms - VOD '1 - U15-V1 (VoD)' ID '150675_f_1065068' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:45,231 - INFO - scraping.cms - VOD '2 - U16-V1 (VoD)' ID '150675_f_1065069' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:45,625 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:bfe9235f5f43ea864ab95c062b5e5e65 with expiry 3600 seconds
2025-05-17 19:44:45,626 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - (|ENGD301|) Engineering Drawing & Design (49)
2025-05-17 19:44:45,627 - INFO - refresh_cache_script - CMS Content refresh summary for seif.elkady: updated=8, skipped=0, failed=0
2025-05-17 19:44:45,628 - INFO - refresh_cache_script - Finished processing for user: seif.elkady
2025-05-17 19:44:45,629 - INFO - refresh_cache_script - CMS Content refresh summary for ahmed.abd-elhamid: updated=8, skipped=0, failed=0
2025-05-17 19:44:45,629 - INFO - refresh_cache_script - Finished processing for user: ahmed.abd-elhamid
2025-05-17 19:44:45,630 - INFO - refresh_cache_script - CMS Content refresh summary for abobakr.bedda: updated=7, skipped=1, failed=0
2025-05-17 19:44:45,630 - INFO - refresh_cache_script - Finished processing for user: abobakr.bedda
2025-05-17 19:44:45,814 - INFO - scraping.cms - VOD '3 - U15 V1 Complete (VoD)' ID '150675_f_1141997' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:46,383 - INFO - scraping.cms - VOD '1 - U14 - V1 (VoD)' ID '150675_f_1063454' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:46,804 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:0c2c75497eb4b19b71cdfa6d68cc86f8 with expiry 3600 seconds
2025-05-17 19:44:46,805 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|MATH404|) Math IV (1072)
2025-05-17 19:44:46,806 - INFO - refresh_cache_script - CMS Content refresh summary for mohamed.elsaadi: updated=8, skipped=0, failed=0
2025-05-17 19:44:46,807 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-17 19:44:46,979 - INFO - scraping.cms - VOD '2 - U14- V2 (VoD)' ID '150675_f_1065039' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:47,560 - INFO - scraping.cms - VOD '3 - U14-V3 (VoD)' ID '150675_f_1065060' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:48,073 - INFO - scraping.cms - VOD '4 - U14-V4 (VoD)' ID '150675_f_1065058' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:48,367 - INFO - scraping.cms - VOD '5 - U14 Complete Video (VoD)' ID '150675_f_1141802' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:49,328 - INFO - scraping.cms - VOD '6 - U14 Complete V2 (VoD)' ID '150675_f_1141799' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:49,665 - INFO - scraping.cms - VOD '1 - K13- V1 (VoD)' ID '150675_f_1062824' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:49,947 - INFO - scraping.cms - VOD '2 - K13 - V2 (VoD)' ID '150675_f_1062876' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:50,524 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:51,086 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:51,607 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:51,963 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-17 19:44:51,964 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:52,557 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:53,120 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:53,512 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:54,088 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:54,642 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:55,171 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:55,435 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:55,956 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:56,543 - INFO - scraping.cms - VOD '1 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:57,113 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:57,646 - INFO - scraping.cms - VOD '3 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:58,010 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:58,343 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-17 19:44:58,344 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:58,708 - INFO - scraping.cms - VOD '1 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:59,263 - INFO - scraping.cms - VOD '1 - kapitel 5 video 12 (VoD)' ID '150675_f_997901' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:59,600 - INFO - scraping.cms - VOD '1 - Unit 6 Video 13 (VoD)' ID '150675_f_1004678' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:44:59,913 - INFO - scraping.cms - VOD '1 - Video 14 (VoD)' ID '150675_f_1010657' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:00,488 - INFO - scraping.cms - VOD '2 - Video 15 (VoD)' ID '150675_f_1010661' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:01,522 - INFO - scraping.cms - VOD '1 - Kapitel 5 Video 11 (VoD)' ID '150675_f_997894' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:02,151 - INFO - scraping.cms - VOD '1 - Unit 4 Video 9 (VoD)' ID '150675_f_983448' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:02,748 - INFO - scraping.cms - VOD '2 - Unit 5 Video 10 (VoD)' ID '150675_f_983452' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:03,413 - INFO - scraping.cms - VOD '1 - Unit 4 Video 8 (VoD)' ID '150675_f_983472' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:04,010 - INFO - scraping.cms - VOD '1 - Unit 2 Video 5 (VoD)' ID '150675_f_967860' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:04,579 - INFO - scraping.cms - VOD '2 - Unit 2 Video 6 (VoD)' ID '150675_f_967861' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:05,148 - INFO - scraping.cms - VOD '3 - Unit 2 Video 7 (VoD)' ID '150675_f_967862' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:05,711 - INFO - scraping.cms - VOD '1 - Unit 1 Video 4 (VoD)' ID '150675_f_967843' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:06,329 - INFO - scraping.cms - VOD '1 - Video 2 (VoD)' ID '150675_f_962606' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:06,944 - INFO - scraping.cms - VOD '2 - Video 3 (VoD)' ID '150675_f_962713' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:07,298 - INFO - scraping.cms - VOD '1 - Video 1 (VoD)' ID '150675_f_962715' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:07,863 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65. Found 36 weeks.
2025-05-17 19:45:09,026 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:e294f40144fc40f87c38784a4811c7b0 with expiry 3600 seconds
2025-05-17 19:45:09,027 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|DE404|) Basic German 4 (73)
2025-05-17 19:45:09,028 - INFO - refresh_cache_script - CMS Content refresh summary for malak.mohamedelkady: updated=8, skipped=0, failed=0
2025-05-17 19:45:09,029 - INFO - refresh_cache_script - Finished processing for user: malak.mohamedelkady
2025-05-17 19:45:09,029 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-17 19:45:09,030 - INFO - refresh_cache_script - User: malak.mohamedelkady -> cms_content: updated=8, skipped=0, failed=0
2025-05-17 19:45:09,031 - INFO - refresh_cache_script - User: malek.amer -> cms_content: updated=7, skipped=0, failed=0
2025-05-17 19:45:09,031 - INFO - refresh_cache_script - User: ahmed.abd-elhamid -> cms_content: updated=8, skipped=0, failed=0
2025-05-17 19:45:09,032 - INFO - refresh_cache_script - User: seif.elkady -> cms_content: updated=8, skipped=0, failed=0
2025-05-17 19:45:09,033 - INFO - refresh_cache_script - User: abobakr.bedda -> cms_content: updated=7, skipped=1, failed=0
2025-05-17 19:45:09,033 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_content: updated=8, skipped=0, failed=0
2025-05-17 19:45:09,034 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-17T19:45:09.034264 (Duration: 0:01:23.272503) ---
2025-05-17 19:45:09,035 - INFO - refresh_cache_script - CMS Content Courses Summary: Updated=46, Skipped=1, Failed=0
2025-05-17 19:45:09,035 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=0, Skipped=0, Failed=0
2025-05-17 19:45:31,630 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-17 19:45:31,631 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-17 19:45:31,631 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-17 19:45:31,633 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-17T19:45:31.633070 ---
2025-05-17 19:45:31,633 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-17 19:45:31,836 - INFO - refresh_cache_script - Refreshing section 4 (cms_content) for 6 users.
2025-05-17 19:45:31,837 - INFO - refresh_cache_script - Starting processing for user: malak.mohamedelkady
2025-05-17 19:45:31,837 - INFO - refresh_cache_script - Processing user: malak.mohamedelkady for data types: ['cms_content']
2025-05-17 19:45:31,838 - INFO - refresh_cache_script - Initiating deep CMS content refresh for user: malak.mohamedelkady
2025-05-17 19:45:31,841 - INFO - scraping.cms - Fetching CMS course list for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-17 19:45:31,842 - INFO - refresh_cache_script - Starting processing for user: malek.amer
2025-05-17 19:45:31,844 - INFO - refresh_cache_script - Processing user: malek.amer for data types: ['cms_content']
2025-05-17 19:45:31,845 - INFO - refresh_cache_script - Initiating deep CMS content refresh for user: malek.amer
2025-05-17 19:45:31,847 - INFO - scraping.cms - Fetching CMS course list for malek.amer from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-17 19:45:31,847 - INFO - refresh_cache_script - Starting processing for user: ahmed.abd-elhamid
2025-05-17 19:45:31,850 - INFO - refresh_cache_script - Processing user: ahmed.abd-elhamid for data types: ['cms_content']
2025-05-17 19:45:31,851 - INFO - refresh_cache_script - Initiating deep CMS content refresh for user: ahmed.abd-elhamid
2025-05-17 19:45:31,852 - INFO - scraping.cms - Fetching CMS course list for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-17 19:45:31,852 - INFO - refresh_cache_script - Starting processing for user: seif.elkady
2025-05-17 19:45:31,853 - INFO - refresh_cache_script - Processing user: seif.elkady for data types: ['cms_content']
2025-05-17 19:45:31,854 - INFO - refresh_cache_script - Initiating deep CMS content refresh for user: seif.elkady
2025-05-17 19:45:31,855 - INFO - scraping.cms - Fetching CMS course list for seif.elkady from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-17 19:45:31,855 - INFO - refresh_cache_script - Starting processing for user: abobakr.bedda
2025-05-17 19:45:31,857 - INFO - refresh_cache_script - Processing user: abobakr.bedda for data types: ['cms_content']
2025-05-17 19:45:31,859 - INFO - refresh_cache_script - Initiating deep CMS content refresh for user: abobakr.bedda
2025-05-17 19:45:31,861 - INFO - scraping.cms - Fetching CMS course list for abobakr.bedda from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-17 19:45:31,861 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-17 19:45:31,863 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_content']
2025-05-17 19:45:31,864 - INFO - refresh_cache_script - Initiating deep CMS content refresh for user: mohamed.elsaadi
2025-05-17 19:45:31,865 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-17 19:45:34,171 - INFO - scraping.cms - Successfully scraped 7 courses for malek.amer.
2025-05-17 19:45:34,173 - INFO - refresh_cache_script - Found 7 courses for malek.amer. Creating refresh tasks.
2025-05-17 19:45:34,174 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:45:34,175 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:45:34,176 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:45:34,178 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:45:34,179 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:45:34,180 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:45:34,183 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-05-17 19:45:34,186 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-05-17 19:45:34,188 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:45:34,189 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:45:34,542 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-17 19:45:34,545 - INFO - refresh_cache_script - Found 8 courses for mohamed.elsaadi. Creating refresh tasks.
2025-05-17 19:45:34,546 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:45:34,547 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:45:34,550 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:45:34,552 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:45:34,554 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:45:34,556 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:45:34,558 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:45:34,559 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:45:34,561 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:45:34,564 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:45:34,569 - INFO - scraping.cms - Successfully scraped 8 courses for abobakr.bedda.
2025-05-17 19:45:34,571 - INFO - refresh_cache_script - Found 8 courses for abobakr.bedda. Creating refresh tasks.
2025-05-17 19:45:34,573 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:45:34,575 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:45:34,576 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:45:34,578 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:45:34,580 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:45:34,581 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:45:34,583 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:45:34,585 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:45:34,588 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:45:34,593 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:45:34,753 - INFO - scraping.cms - Successfully scraped 8 courses for ahmed.abd-elhamid.
2025-05-17 19:45:34,754 - INFO - refresh_cache_script - Found 8 courses for ahmed.abd-elhamid. Creating refresh tasks.
2025-05-17 19:45:34,755 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:45:34,756 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:45:34,758 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:45:34,760 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:45:34,761 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:45:34,762 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:45:34,763 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:45:34,766 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:45:34,768 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:45:34,769 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:45:34,956 - INFO - scraping.cms - Successfully scraped 8 courses for seif.elkady.
2025-05-17 19:45:34,958 - INFO - refresh_cache_script - Found 8 courses for seif.elkady. Creating refresh tasks.
2025-05-17 19:45:34,959 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:45:34,959 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:45:34,961 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:45:34,962 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:45:34,964 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:45:34,966 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:45:34,968 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:45:34,969 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:45:34,974 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:45:34,983 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:45:35,742 - INFO - scraping.cms - Successfully scraped 8 courses for malak.mohamedelkady.
2025-05-17 19:45:35,743 - INFO - refresh_cache_script - Found 8 courses for malak.mohamedelkady. Creating refresh tasks.
2025-05-17 19:45:35,744 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-05-17 19:45:35,745 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-05-17 19:45:35,746 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-05-17 19:45:35,747 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-05-17 19:45:35,750 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-05-17 19:45:35,751 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-05-17 19:45:35,755 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-05-17 19:45:35,757 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-05-17 19:45:35,758 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-05-17 19:45:35,760 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-05-17 19:45:37,120 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:45:37,162 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-05-17 19:45:37,163 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-05-17 19:45:37,164 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-05-17 19:45:37,165 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-05-17 19:45:37,165 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-05-17 19:45:37,166 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-05-17 19:45:37,167 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-05-17 19:45:37,168 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:45:37,169 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:37,792 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:45:37,793 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:37,809 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:45:37,810 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:45:37,821 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-05-17 19:45:37,822 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-05-17 19:45:37,822 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-05-17 19:45:37,823 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-05-17 19:45:37,824 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-05-17 19:45:37,825 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-05-17 19:45:37,826 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-05-17 19:45:37,830 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:45:37,832 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:37,903 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 10 weeks.
2025-05-17 19:45:37,971 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:45:38,253 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:45:38,383 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-05-17 19:45:38,385 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-05-17 19:45:38,389 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 10 weeks.
2025-05-17 19:45:38,390 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-05-17 19:45:38,392 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:38,445 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:45:38,446 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:38,492 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:45:38,510 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:45:38,562 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-05-17 19:45:38,563 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-05-17 19:45:38,564 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-05-17 19:45:38,565 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-05-17 19:45:38,566 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-05-17 19:45:38,566 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-05-17 19:45:38,567 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-05-17 19:45:38,568 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:45:38,569 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:38,713 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:45:38,753 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-17 19:45:38,779 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-05-17 19:45:38,780 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-05-17 19:45:38,781 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-05-17 19:45:38,782 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:38,979 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-05-17 19:45:38,980 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-05-17 19:45:38,981 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-05-17 19:45:38,982 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-05-17 19:45:38,982 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-05-17 19:45:38,983 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-05-17 19:45:38,984 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-05-17 19:45:38,985 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:45:38,987 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:38,998 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:45:39,049 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:45:39,053 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-05-17 19:45:39,079 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65. Found 12 weeks.
2025-05-17 19:45:39,145 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:45:39,146 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:39,152 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 11 weeks.
2025-05-17 19:45:39,397 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-05-17 19:45:39,597 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:45:39,598 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:39,674 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-05-17 19:45:39,749 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-05-17 19:45:39,751 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-05-17 19:45:39,752 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-05-17 19:45:39,753 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:39,785 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-05-17 19:45:39,823 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-05-17 19:45:39,825 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-05-17 19:45:39,826 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-05-17 19:45:39,827 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-05-17 19:45:39,828 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-05-17 19:45:39,829 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-05-17 19:45:39,830 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-05-17 19:45:39,831 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:45:39,832 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:39,904 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:39,945 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65. Found 8 weeks.
2025-05-17 19:45:39,948 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-05-17 19:45:40,182 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-05-17 19:45:40,207 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-17 19:45:40,208 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:40,235 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:6d4b66a6639821a41615818b09fae811 with expiry 3600 seconds
2025-05-17 19:45:40,236 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for abobakr.bedda - (|SM101|) Scientific Methods (A1) (16)
2025-05-17 19:45:40,237 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:45:40,239 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:45:40,263 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-05-17 19:45:40,264 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-05-17 19:45:40,265 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-05-17 19:45:40,266 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:40,339 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:40,374 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:40,503 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 13 weeks.
2025-05-17 19:45:40,568 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:45:40,594 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 10 weeks.
2025-05-17 19:45:40,624 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-05-17 19:45:40,626 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-05-17 19:45:40,628 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-05-17 19:45:40,630 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:40,654 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 11 weeks.
2025-05-17 19:45:40,671 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 11 weeks.
2025-05-17 19:45:40,679 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:40,736 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:40,860 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-05-17 19:45:40,916 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:41,001 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 11 weeks.
2025-05-17 19:45:41,033 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 13 weeks.
2025-05-17 19:45:41,037 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 13 weeks.
2025-05-17 19:45:41,088 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:41,234 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65. Found 11 weeks.
2025-05-17 19:45:41,247 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:45:41,319 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:45:41,346 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 12 weeks.
2025-05-17 19:45:41,347 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-05-17 19:45:41,350 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-05-17 19:45:41,352 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:45:41,371 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:41,481 - INFO - scraping.cms - VOD '2 - Blockchain Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b3790955-857d-ac72-102a-6566cc43a321' for access URL.
2025-05-17 19:45:41,482 - INFO - scraping.cms - VOD '3 - Tutorial 5 VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-58216c6b-b89b-8fcd-d41f-d8c99c564067' for access URL.
2025-05-17 19:45:41,484 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65. Found 11 weeks.
2025-05-17 19:45:41,587 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:41,597 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:818ee80513a8c43769d64244cd74d46a with expiry 3600 seconds
2025-05-17 19:45:41,598 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|HROB203|) Human Resources Management for BI (2488)
2025-05-17 19:45:41,925 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-17 19:45:41,926 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:42,055 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:42,140 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:42,411 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-05-17 19:45:42,494 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:45:42,559 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:45:42,577 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 11 weeks.
2025-05-17 19:45:42,643 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:45:42,685 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-17 19:45:42,769 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65. Found 17 weeks.
2025-05-17 19:45:42,847 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:42,882 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-17 19:45:42,882 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:42,965 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 12 weeks.
2025-05-17 19:45:42,982 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:43,045 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:0273cfb9d74dacfa10a29ddad35614a9 with expiry 3600 seconds
2025-05-17 19:45:43,046 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - (|PHYS202|) Physics II (450)
2025-05-17 19:45:43,163 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:43,185 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:43,308 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 12 weeks.
2025-05-17 19:45:43,467 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:43,703 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:43,775 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:43,846 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65. Found 14 weeks.
2025-05-17 19:45:43,908 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:44,118 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:44,132 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:45:44,189 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:45:44,313 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:45:44,347 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:******************************** with expiry 3600 seconds
2025-05-17 19:45:44,348 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - (|SM101|) Scientific Methods (A1) (16)
2025-05-17 19:45:44,367 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65. Found 11 weeks.
2025-05-17 19:45:44,389 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-05-17 19:45:44,395 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:44,531 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:44,564 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:45:44,598 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:44,898 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:44,900 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:45:44,985 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:45,063 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 12 weeks.
2025-05-17 19:45:45,188 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-17 19:45:45,189 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:45,214 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:45,448 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:74760149fa9e6db7299f93ae95bd5a99 with expiry 3600 seconds
2025-05-17 19:45:45,449 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malek.amer - (|EDPT201|) Production Technology (432)
2025-05-17 19:45:45,498 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:45,543 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:45,778 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:45,793 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:45,880 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:46,055 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:46,185 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:46,186 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:46,377 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:46,509 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:46,593 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:bf3a6dfb2a4c8c41858dc8db75d5c026 with expiry 3600 seconds
2025-05-17 19:45:46,594 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malek.amer - (|SM101|) Scientific Methods (A1) (16)
2025-05-17 19:45:46,596 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-05-17 19:45:46,596 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-05-17 19:45:46,599 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65. Found 17 weeks.
2025-05-17 19:45:46,599 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:45:46,601 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:45:46,603 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:45:46,604 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:45:46,606 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:45:46,608 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:45:46,609 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:45:46,610 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:45:46,646 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:46,755 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:46,991 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:47,088 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:47,142 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:47,144 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:45:47,361 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:47,454 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:47,488 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-05-17 19:45:47,672 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:47,704 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:47,788 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:47,894 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:65b03f76c617f30ea9ca30bcd51951a9 with expiry 3600 seconds
2025-05-17 19:45:47,895 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malek.amer - (|MATH203|) Mathematics I (17)
2025-05-17 19:45:47,966 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-17 19:45:47,968 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:48,016 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:48,093 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-05-17 19:45:48,290 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-05-17 19:45:48,347 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:48,374 - INFO - scraping.cms - VOD '2 - RPW-  Introduction of the literature review (VoD)' ID '150675_f_1056943' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:48,623 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:48,650 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:48,687 - INFO - scraping.cms - VOD '4 - RPW- Literature Review (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-51ed902d-c21f-51d3-1e23-de32b20bb82d' for access URL.
2025-05-17 19:45:48,688 - INFO - scraping.cms - VOD '6 - RPW- Methodology Section (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-607130fb-d676-62ac-7bfb-8e2cd817db36' for access URL.
2025-05-17 19:45:48,690 - INFO - scraping.cms - VOD '2 - RPW- Article Review (VoD)' ID '150675_f_1056934' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:48,970 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:45:48,995 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-17 19:45:49,002 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:49,029 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:e980688428bad3a4e5d5de5383c83946 with expiry 3600 seconds
2025-05-17 19:45:49,030 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - (|CSEN202|) Introduction to Computer Programming (19)
2025-05-17 19:45:49,149 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:49,182 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:49,285 - INFO - scraping.cms - VOD '2 - RPW Narrowing Down Steps (Scopus Database) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-bb767829-e552-91a0-eb6a-990d36e54ed2' for access URL.
2025-05-17 19:45:49,286 - INFO - scraping.cms - VOD '4 - RPW Narrowing Down Steps (EBSCOhost Database) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-906e2a09-9ac9-17b7-73d2-947aca7308ee' for access URL.
2025-05-17 19:45:49,288 - INFO - scraping.cms - VOD '4 - RPW- Research Terminology (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-d8fa6938-9caa-7132-215a-425f77ef607e' for access URL.
2025-05-17 19:45:49,289 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65. Found 5 weeks.
2025-05-17 19:45:49,347 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:49,368 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:45:49,512 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:49,529 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-17 19:45:49,531 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:49,619 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:49,711 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:49,899 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:50,003 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 13 weeks.
2025-05-17 19:45:50,165 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-05-17 19:45:50,183 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:8dbe3d6d795d967a224c77531426cee5 with expiry 3600 seconds
2025-05-17 19:45:50,184 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - (|PHYS202|) Physics II (450)
2025-05-17 19:45:50,259 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-17 19:45:50,260 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:50,286 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:50,390 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:45:50,588 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:50,762 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 10 weeks.
2025-05-17 19:45:50,886 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-17 19:45:50,886 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:50,888 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:51,214 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-05-17 19:45:51,360 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:78a6d7823b573650cd954a14073a8bd2 with expiry 3600 seconds
2025-05-17 19:45:51,361 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for abobakr.bedda - (|PHYS202|) Physics II (450)
2025-05-17 19:45:51,482 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:51,798 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:52,328 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:52,505 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:f6b71efcc7f9320b9f19caac58cb9b80 with expiry 3600 seconds
2025-05-17 19:45:52,506 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - (|SM101|) Scientific Methods (A1) (16)
2025-05-17 19:45:52,892 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:53,251 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:53,587 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:53,643 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:f8e39e2adb24854f36d9a72a3a187964 with expiry 3600 seconds
2025-05-17 19:45:53,644 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - (|PHYS202|) Physics II (450)
2025-05-17 19:45:54,144 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:54,682 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:54,930 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:9498fb390f46840b3f1f0c28cf2518fc with expiry 3600 seconds
2025-05-17 19:45:54,931 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for abobakr.bedda - (|MATH203|) Mathematics I (17)
2025-05-17 19:45:55,251 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:55,826 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:56,232 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:cc237221e139fa9410b370a25ff75444 with expiry 3600 seconds
2025-05-17 19:45:56,233 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - (|MATH203|) Mathematics I (17)
2025-05-17 19:45:56,344 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:56,845 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-17 19:45:56,847 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:45:57,255 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:5056725a2e1d095a8b1519daa2e9895e with expiry 3600 seconds
2025-05-17 19:45:57,256 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|BINF405|) Information and Communication Architecture II (486)
2025-05-17 19:45:57,424 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-05-17 19:45:58,413 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:7d03f64d386af7cd72d63242d2c96bbd with expiry 3600 seconds
2025-05-17 19:45:58,414 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - (|CSEN202|) Introduction to Computer Programming (19)
2025-05-17 19:46:00,667 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:e0724bb0d12f6e68506d5000018dbd47 with expiry 3600 seconds
2025-05-17 19:46:00,668 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - (|ELCT201|) Digital Logic Design (79)
2025-05-17 19:46:01,871 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:bc53a4607c4dae46535866aa2fcd7676 with expiry 3600 seconds
2025-05-17 19:46:01,872 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - (|SM101|) Scientific Methods (A1) (16)
2025-05-17 19:46:02,905 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:1ff8172699c20c7d216ba3df453f68ad with expiry 3600 seconds
2025-05-17 19:46:02,906 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|BINF406|) Digital Transformation (2708)
2025-05-17 19:46:04,054 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:4462b27cf56e0798d09f300eeeb65adb with expiry 3600 seconds
2025-05-17 19:46:04,056 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malek.amer - (|PHYS202|) Physics II (450)
2025-05-17 19:46:05,205 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:4d7ae447c4fe66c338e1951549466285 with expiry 3600 seconds
2025-05-17 19:46:05,206 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - (|ENGD301|) Engineering Drawing & Design (49)
2025-05-17 19:46:06,690 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:f97693fa9ffca93f43c8f520cbf667fe with expiry 3600 seconds
2025-05-17 19:46:06,691 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malek.amer - (|ELCT201|) Digital Logic Design (79)
2025-05-17 19:46:08,200 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:008ad4cc6e77b619200be2e49c404190 with expiry 3600 seconds
2025-05-17 19:46:08,201 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - (|ELCT201|) Digital Logic Design (79)
2025-05-17 19:46:09,357 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:9fa67215c085dc81283eafaa5bbb4610 with expiry 3600 seconds
2025-05-17 19:46:09,358 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|CSEN401|) Computer Programming Lab (402)
2025-05-17 19:46:10,399 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:63af6194da837cf58d78115f83ccf5c9 with expiry 3600 seconds
2025-05-17 19:46:10,401 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|CSEN404|) Introduction to Networks (510)
2025-05-17 19:46:11,554 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:45438db64e5444f582aa00b0bb3daa33 with expiry 3600 seconds
2025-05-17 19:46:11,555 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - (|CSEN202|) Introduction to Computer Programming (19)
2025-05-17 19:46:13,040 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:e70db9b896c65c16d0285ef5fa22aa5d with expiry 3600 seconds
2025-05-17 19:46:13,041 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for abobakr.bedda - (|ELCT201|) Digital Logic Design (79)
2025-05-17 19:46:13,043 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:46:13,043 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:46:13,045 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:46:13,046 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:46:13,048 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:46:13,050 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:46:13,051 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:46:13,053 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:46:13,054 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:46:13,057 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:46:13,059 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:46:13,062 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:46:13,064 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:46:13,067 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:46:13,070 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-05-17 19:46:13,071 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-05-17 19:46:13,074 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:46:13,075 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:46:13,078 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-05-17 19:46:13,081 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-05-17 19:46:13,083 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:46:13,087 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:46:14,130 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-05-17 19:46:14,273 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:46:14,277 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:57ef0654c365fb79601466bd6aa9e55b with expiry 3600 seconds
2025-05-17 19:46:14,279 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for abobakr.bedda - (|ENGD301|) Engineering Drawing & Design (49)
2025-05-17 19:46:15,301 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:943a7189d2212dfcf05e31a9d6ee610c with expiry 3600 seconds
2025-05-17 19:46:15,302 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malek.amer - Engineering - 2nd Orientation course
2025-05-17 19:46:16,328 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:2040fe230931ce0bc66083b58bc494f5 with expiry 3600 seconds
2025-05-17 19:46:16,331 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - (|DE202|) Basic German 2 (33)
2025-05-17 19:46:16,598 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-17 19:46:16,688 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 10 weeks.
2025-05-17 19:46:16,759 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-05-17 19:46:16,797 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-17 19:46:16,913 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-17 19:46:16,962 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:46:16,997 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:46:17,202 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:17,254 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-05-17 19:46:17,257 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-17 19:46:17,269 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-05-17 19:46:17,418 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:991454acfa6066cb44b51e7b641a315d with expiry 3600 seconds
2025-05-17 19:46:17,419 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|RPW401|) Research Paper Writing (A2) (34)
2025-05-17 19:46:17,893 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 12 weeks.
2025-05-17 19:46:18,051 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:18,119 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65. Found 17 weeks.
2025-05-17 19:46:18,122 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:46:18,552 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-17 19:46:18,683 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 13 weeks.
2025-05-17 19:46:18,793 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:2a5b20f14dad6774d3cf5929d32e776a with expiry 3600 seconds
2025-05-17 19:46:18,794 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - (|MATH203|) Mathematics I (17)
2025-05-17 19:46:19,834 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:8b3eb88d297eef9f6b09e272cc8a10f2 with expiry 3600 seconds
2025-05-17 19:46:19,835 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for abobakr.bedda - (|DE202|) Basic German 2 (33)
2025-05-17 19:46:21,037 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:a3c6b37335fdd765767dd80df3cf895c with expiry 3600 seconds
2025-05-17 19:46:21,038 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malek.amer - (|CSEN202|) Introduction to Computer Programming (19)
2025-05-17 19:46:21,256 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-05-17 19:46:21,269 - INFO - scraping.cms - VOD '4 - Final Revision - Part 1 (VoD)' ID '150675_f_1067515' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:21,295 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65. Found 17 weeks.
2025-05-17 19:46:21,798 - INFO - scraping.cms - VOD '5 - Final Revision: Part-2 (VoD)' ID '150675_f_1067508' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:22,077 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:e6b2b8002d98e046cb5176855f29605a with expiry 3600 seconds
2025-05-17 19:46:22,079 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - (|DE202|) Basic German 2 (33)
2025-05-17 19:46:22,379 - INFO - scraping.cms - VOD '6 - Final Revision: Part-3 (VoD)' ID '150675_f_1067512' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:22,462 - INFO - scraping.cms - VOD '1 - Revision Part I (VoD)' ID '150675_f_1123696' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:23,027 - INFO - scraping.cms - VOD '3 - Tutorial 11 (ws 11) (VoD)' ID '150675_f_1069055' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:23,052 - INFO - scraping.cms - VOD '1 - Lektion 24 Video 1 (VoD)' ID '150675_f_1073495' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:23,157 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:d3acd0d76918ba01725396c4fd0e176b with expiry 3600 seconds
2025-05-17 19:46:23,158 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - (|DE202|) Basic German 2 (33)
2025-05-17 19:46:23,219 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-05-17 19:46:23,426 - INFO - scraping.cms - VOD '3 - Tutorial 10 (ws 10) (VoD)' ID '150675_f_1069054' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:23,645 - INFO - scraping.cms - VOD '2 - Lektion 24 Video 1 Komplett (VoD)' ID '150675_f_1073500' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:23,987 - INFO - scraping.cms - VOD '3 - Tutorial 9 (ws 9) (VoD)' ID '150675_f_1069048' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:24,164 - INFO - scraping.cms - VOD '3 - Lektion 24 Video 2 Komplett (VoD)' ID '150675_f_1073502' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:24,231 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:76346a5a03930d128cfaaad5683d2c22 with expiry 3600 seconds
2025-05-17 19:46:24,232 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - Engineering - 2nd Orientation course
2025-05-17 19:46:24,545 - INFO - scraping.cms - VOD '3 - Tutorial 8 (ws 8) (VoD)' ID '150675_f_1069045' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:24,735 - INFO - scraping.cms - VOD '1 - Lektion 23 Video 1 (VoD)' ID '150675_f_1073450' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:25,126 - INFO - scraping.cms - VOD '3 - Tutorial 7 (ws 7) (VoD)' ID '150675_f_1069042' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:25,304 - INFO - scraping.cms - VOD '2 - Lektion 23 Video 2 (VoD)' ID '150675_f_1073453' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:25,381 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:e715d21507618ab36722c375ea8b23e3 with expiry 3600 seconds
2025-05-17 19:46:25,382 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for abobakr.bedda - (|CSEN202|) Introduction to Computer Programming (19)
2025-05-17 19:46:25,718 - INFO - scraping.cms - VOD '3 - Tutorial 6 (ws 6) (VoD)' ID '150675_f_1069039' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:25,891 - INFO - scraping.cms - VOD '3 - Lektion 23 Video 3 (VoD)' ID '150675_f_1073456' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:26,100 - INFO - scraping.cms - VOD '4 - Midterm Revision (VoD)' ID '150675_f_1071230' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:26,404 - INFO - scraping.cms - VOD '4 - Lektion 23 Video 1 Komplett (VoD)' ID '150675_f_1073474' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:26,417 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:026f8dd3a7fdf3cffe83226f2147261d with expiry 3600 seconds
2025-05-17 19:46:26,418 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for abobakr.bedda - Engineering - 2nd Orientation course
2025-05-17 19:46:26,621 - INFO - scraping.cms - VOD '3 - Tutorial 5 (ws 5) (VoD)' ID '150675_f_1071247' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:26,982 - INFO - scraping.cms - VOD '5 - Lektion 23 Video 2 Komplett (VoD)' ID '150675_f_1073481' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:27,185 - INFO - scraping.cms - VOD '3 - Tutorial 4 (ws 4) (VoD)' ID '150675_f_1067374' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:27,325 - INFO - scraping.cms - VOD '6 - Lektion 23 Video 3 Komplett (VoD)' ID '150675_f_1073486' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:27,431 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:b2ed3a725884d8c0eab875a68dbf6ffe with expiry 3600 seconds
2025-05-17 19:46:27,432 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - Engineering - 2nd Orientation course
2025-05-17 19:46:27,642 - INFO - scraping.cms - VOD '7 - Lektion 23 Video 4 Komplett (VoD)' ID '150675_f_1073485' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:27,760 - INFO - scraping.cms - VOD '3 - Tutorial 3 (ws 3) (VoD)' ID '150675_f_1066977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:27,951 - INFO - scraping.cms - VOD '1 - Lektion 22 Video 1 (VoD)' ID '150675_f_1073442' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:28,077 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65. Found 16 weeks.
2025-05-17 19:46:28,236 - INFO - scraping.cms - VOD '2 - Lektion 22 Video 2 (VoD)' ID '150675_f_1073444' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:28,480 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:3de3baf04b6cc6442380c22fb0dd1329 with expiry 3600 seconds
2025-05-17 19:46:28,481 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - Engineering - 2nd Orientation course
2025-05-17 19:46:28,518 - INFO - scraping.cms - VOD '3 - Lektion 22 complete video 1 (VoD)' ID '150675_f_1073448' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:28,800 - INFO - scraping.cms - VOD '4 - Lektion 22 complete video 2 (VoD)' ID '150675_f_1101615' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:29,301 - INFO - scraping.cms - VOD '1 - Unit 21 Part 1 (VoD)' ID '150675_f_1063834' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:29,610 - INFO - scraping.cms - VOD '2 - Unit 21 Part 2 (VoD)' ID '150675_f_1067572' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:29,951 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:72d50b4aae2d29754439a6f472414afb with expiry 3600 seconds
2025-05-17 19:46:29,952 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - (|ELCT201|) Digital Logic Design (79)
2025-05-17 19:46:30,188 - INFO - scraping.cms - VOD '3 - Unit 21 (Complete Unit: Part 1) (VoD)' ID '150675_f_1094812' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:30,481 - INFO - scraping.cms - VOD '4 - Unit 21 (Complete Unit: Part 2) (VoD)' ID '150675_f_1094820' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:30,796 - INFO - scraping.cms - VOD '5 - Unit 21 (Complete Unit: Part 3) (VoD)' ID '150675_f_1094828' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:31,059 - INFO - scraping.cms - VOD '1 - Unit 20 (VoD)' ID '150675_f_1063828' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:31,096 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:bfe9235f5f43ea864ab95c062b5e5e65 with expiry 3600 seconds
2025-05-17 19:46:31,098 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for mohamed.elsaadi - (|ENGD301|) Engineering Drawing & Design (49)
2025-05-17 19:46:31,593 - INFO - scraping.cms - VOD '2 - Unit 20 (Complete Unit) (VoD)' ID '150675_f_1092097' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:32,098 - INFO - scraping.cms - VOD '3 - K19 - V1 (VoD)' ID '150675_f_1062891' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:32,404 - INFO - scraping.cms - VOD '4 - Unit 19 Complete Unit (Part1) (VoD)' ID '150675_f_1066246' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:32,426 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:bce5e54a17608dbc809d36caec90886f with expiry 3600 seconds
2025-05-17 19:46:32,426 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for seif.elkady - (|MATH203|) Mathematics I (17)
2025-05-17 19:46:32,427 - INFO - refresh_cache_script - CMS Content refresh summary for malek.amer: updated=7, skipped=0, failed=0
2025-05-17 19:46:32,428 - INFO - refresh_cache_script - Finished processing for user: malek.amer
2025-05-17 19:46:32,665 - INFO - scraping.cms - VOD '5 - Unit 19 Complete Unit (Part 2) (VoD)' ID '150675_f_1066249' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:33,189 - INFO - scraping.cms - VOD '1 - Revision 1 (VoD)' ID '150675_f_1124540' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:33,609 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:835c207cbf8ea3f369d1ef2b4518231e with expiry 3600 seconds
2025-05-17 19:46:33,610 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for ahmed.abd-elhamid - (|ENGD301|) Engineering Drawing & Design (49)
2025-05-17 19:46:33,611 - INFO - refresh_cache_script - CMS Content refresh summary for abobakr.bedda: updated=8, skipped=0, failed=0
2025-05-17 19:46:33,612 - INFO - refresh_cache_script - Finished processing for user: abobakr.bedda
2025-05-17 19:46:33,707 - INFO - scraping.cms - VOD '2 - Revision 2 (VoD)' ID '150675_f_1124541' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:34,043 - INFO - scraping.cms - VOD '4 - Model test DE3 AUDIO (VoD)' ID '150675_f_1124235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:34,573 - INFO - scraping.cms - VOD '1 - U18-V1 Blended (VoD)' ID '150675_f_1090464' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:34,783 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:0c2c75497eb4b19b71cdfa6d68cc86f8 with expiry 3600 seconds
2025-05-17 19:46:34,784 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|MATH404|) Math IV (1072)
2025-05-17 19:46:34,785 - INFO - refresh_cache_script - CMS Content refresh summary for mohamed.elsaadi: updated=8, skipped=0, failed=0
2025-05-17 19:46:34,785 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-17 19:46:34,786 - INFO - refresh_cache_script - CMS Content refresh summary for seif.elkady: updated=8, skipped=0, failed=0
2025-05-17 19:46:34,787 - INFO - refresh_cache_script - Finished processing for user: seif.elkady
2025-05-17 19:46:34,787 - INFO - refresh_cache_script - CMS Content refresh summary for ahmed.abd-elhamid: updated=8, skipped=0, failed=0
2025-05-17 19:46:34,788 - INFO - refresh_cache_script - Finished processing for user: ahmed.abd-elhamid
2025-05-17 19:46:35,182 - INFO - scraping.cms - VOD '2 - U18-V2 Blended (VoD)' ID '150675_f_1090082' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:35,531 - INFO - scraping.cms - VOD '3 - U18-V1 Complete (VoD)' ID '150675_f_1090084' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:36,106 - INFO - scraping.cms - VOD '4 - U18-V2 Complete (VoD)' ID '150675_f_1090083' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:36,673 - INFO - scraping.cms - VOD '1 - U17- V1 Blended (VoD)' ID '150675_f_1090058' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:37,255 - INFO - scraping.cms - VOD '2 - U17-V2 Blended (VoD)' ID '150675_f_1090062' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:37,515 - INFO - scraping.cms - VOD '3 - U17-V3 Blended (VoD)' ID '150675_f_1090064' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:38,079 - INFO - scraping.cms - VOD '4 - U17 - V1 Complete (VoD)' ID '150675_f_1090063' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:38,411 - INFO - scraping.cms - VOD '5 - U17 - V2 Complete (VoD)' ID '150675_f_1090067' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:38,943 - INFO - scraping.cms - VOD '6 - U17-V3 Complete (VoD)' ID '150675_f_1090071' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:39,246 - INFO - scraping.cms - VOD '1 - U16- V1 Blended (VoD)' ID '150675_f_1090053' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:39,521 - INFO - scraping.cms - VOD '2 - U16- V1 complete (VoD)' ID '150675_f_1090057' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:39,818 - INFO - scraping.cms - VOD '3 - U16-V2 complete (VoD)' ID '150675_f_1090462' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:40,401 - INFO - scraping.cms - VOD '1 - U13-complete Video1 (VoD)' ID '150675_f_1065580' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:40,725 - INFO - scraping.cms - VOD '2 - U13- complete Video2 (VoD)' ID '150675_f_1065582' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:41,009 - INFO - scraping.cms - VOD '1 - U15-V1 (VoD)' ID '150675_f_1065068' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:41,562 - INFO - scraping.cms - VOD '2 - U16-V1 (VoD)' ID '150675_f_1065069' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:42,127 - INFO - scraping.cms - VOD '3 - U15 V1 Complete (VoD)' ID '150675_f_1141997' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:42,453 - INFO - scraping.cms - VOD '1 - U14 - V1 (VoD)' ID '150675_f_1063454' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:43,044 - INFO - scraping.cms - VOD '2 - U14- V2 (VoD)' ID '150675_f_1065039' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:43,323 - INFO - scraping.cms - VOD '3 - U14-V3 (VoD)' ID '150675_f_1065060' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:43,621 - INFO - scraping.cms - VOD '4 - U14-V4 (VoD)' ID '150675_f_1065058' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:44,218 - INFO - scraping.cms - VOD '5 - U14 Complete Video (VoD)' ID '150675_f_1141802' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:44,791 - INFO - scraping.cms - VOD '6 - U14 Complete V2 (VoD)' ID '150675_f_1141799' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:45,408 - INFO - scraping.cms - VOD '1 - K13- V1 (VoD)' ID '150675_f_1062824' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:45,735 - INFO - scraping.cms - VOD '2 - K13 - V2 (VoD)' ID '150675_f_1062876' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:45,986 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:46,508 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:47,086 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:47,394 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-17 19:46:47,395 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:47,931 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:48,497 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:49,090 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:49,655 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:50,236 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:50,787 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:51,108 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:51,395 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:51,928 - INFO - scraping.cms - VOD '1 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:52,501 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:52,866 - INFO - scraping.cms - VOD '3 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:53,414 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:53,982 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-17 19:46:53,983 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:54,590 - INFO - scraping.cms - VOD '1 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:55,111 - INFO - scraping.cms - VOD '1 - kapitel 5 video 12 (VoD)' ID '150675_f_997901' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:55,670 - INFO - scraping.cms - VOD '1 - Unit 6 Video 13 (VoD)' ID '150675_f_1004678' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:56,027 - INFO - scraping.cms - VOD '1 - Video 14 (VoD)' ID '150675_f_1010657' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:56,638 - INFO - scraping.cms - VOD '2 - Video 15 (VoD)' ID '150675_f_1010661' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:57,155 - INFO - scraping.cms - VOD '1 - Kapitel 5 Video 11 (VoD)' ID '150675_f_997894' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:57,456 - INFO - scraping.cms - VOD '1 - Unit 4 Video 9 (VoD)' ID '150675_f_983448' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:58,025 - INFO - scraping.cms - VOD '2 - Unit 5 Video 10 (VoD)' ID '150675_f_983452' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:58,601 - INFO - scraping.cms - VOD '1 - Unit 4 Video 8 (VoD)' ID '150675_f_983472' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:59,234 - INFO - scraping.cms - VOD '1 - Unit 2 Video 5 (VoD)' ID '150675_f_967860' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:46:59,566 - INFO - scraping.cms - VOD '2 - Unit 2 Video 6 (VoD)' ID '150675_f_967861' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:47:00,238 - INFO - scraping.cms - VOD '3 - Unit 2 Video 7 (VoD)' ID '150675_f_967862' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:47:00,871 - INFO - scraping.cms - VOD '1 - Unit 1 Video 4 (VoD)' ID '150675_f_967843' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:47:01,424 - INFO - scraping.cms - VOD '1 - Video 2 (VoD)' ID '150675_f_962606' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:47:01,939 - INFO - scraping.cms - VOD '2 - Video 3 (VoD)' ID '150675_f_962713' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:47:02,543 - INFO - scraping.cms - VOD '1 - Video 1 (VoD)' ID '150675_f_962715' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-17 19:47:03,065 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65. Found 36 weeks.
2025-05-17 19:47:04,229 - INFO - refresh_cache_script - Set PICKLE cache for key cms_content:e294f40144fc40f87c38784a4811c7b0 with expiry 3600 seconds
2025-05-17 19:47:04,230 - INFO - refresh_cache_script - Successfully refreshed CMS content cache for malak.mohamedelkady - (|DE404|) Basic German 4 (73)
2025-05-17 19:47:04,231 - INFO - refresh_cache_script - CMS Content refresh summary for malak.mohamedelkady: updated=8, skipped=0, failed=0
2025-05-17 19:47:04,232 - INFO - refresh_cache_script - Finished processing for user: malak.mohamedelkady
2025-05-17 19:47:04,233 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-17 19:47:04,233 - INFO - refresh_cache_script - User: malak.mohamedelkady -> cms_content: updated=8, skipped=0, failed=0
2025-05-17 19:47:04,234 - INFO - refresh_cache_script - User: malek.amer -> cms_content: updated=7, skipped=0, failed=0
2025-05-17 19:47:04,235 - INFO - refresh_cache_script - User: ahmed.abd-elhamid -> cms_content: updated=8, skipped=0, failed=0
2025-05-17 19:47:04,235 - INFO - refresh_cache_script - User: seif.elkady -> cms_content: updated=8, skipped=0, failed=0
2025-05-17 19:47:04,236 - INFO - refresh_cache_script - User: abobakr.bedda -> cms_content: updated=8, skipped=0, failed=0
2025-05-17 19:47:04,236 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_content: updated=8, skipped=0, failed=0
2025-05-17 19:47:04,237 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-17T19:47:04.237410 (Duration: 0:01:32.604340) ---
2025-05-17 19:47:04,237 - INFO - refresh_cache_script - CMS Content Courses Summary: Updated=47, Skipped=0, Failed=0
2025-05-17 19:47:04,238 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=0, Skipped=0, Failed=0
2025-05-18 21:44:54,050 - INFO - refresh_cache_script - Notifications enabled for users: ALL
2025-05-18 21:44:54,050 - INFO - refresh_cache_script - Max concurrent fetches per user set to default: 5
2025-05-18 21:44:54,051 - INFO - refresh_cache_script - Max concurrent users set to default: 10
2025-05-18 21:44:54,055 - INFO - refresh_cache_script - --- Cache Refresh Script Started: 2025-05-18T21:44:54.055164 ---
2025-05-18 21:44:54,055 - INFO - refresh_cache_script - Retrieving user credentials...
2025-05-18 21:44:54,258 - INFO - refresh_cache_script - Refreshing section 4 (cms_content) for 7 users.
2025-05-18 21:44:54,259 - INFO - refresh_cache_script - Starting processing for user: malak.mohamedelkady
2025-05-18 21:44:54,259 - INFO - refresh_cache_script - Processing user: malak.mohamedelkady for data types: ['cms_content']
2025-05-18 21:44:54,260 - INFO - refresh_cache_script - Processing CMS content section for user: malak.mohamedelkady
2025-05-18 21:44:54,265 - INFO - scraping.cms - Fetching CMS course list for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-18 21:44:54,265 - INFO - refresh_cache_script - Starting processing for user: malek.amer
2025-05-18 21:44:54,266 - INFO - refresh_cache_script - Processing user: malek.amer for data types: ['cms_content']
2025-05-18 21:44:54,267 - INFO - refresh_cache_script - Processing CMS content section for user: malek.amer
2025-05-18 21:44:54,267 - INFO - scraping.cms - Fetching CMS course list for malek.amer from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-18 21:44:54,268 - INFO - refresh_cache_script - Starting processing for user: ahmed.abd-elhamid
2025-05-18 21:44:54,270 - INFO - refresh_cache_script - Processing user: ahmed.abd-elhamid for data types: ['cms_content']
2025-05-18 21:44:54,270 - INFO - refresh_cache_script - Processing CMS content section for user: ahmed.abd-elhamid
2025-05-18 21:44:54,271 - INFO - scraping.cms - Fetching CMS course list for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-18 21:44:54,271 - INFO - refresh_cache_script - Starting processing for user: seif.elkady
2025-05-18 21:44:54,273 - INFO - refresh_cache_script - Processing user: seif.elkady for data types: ['cms_content']
2025-05-18 21:44:54,273 - INFO - refresh_cache_script - Processing CMS content section for user: seif.elkady
2025-05-18 21:44:54,274 - INFO - scraping.cms - Fetching CMS course list for seif.elkady from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-18 21:44:54,275 - INFO - refresh_cache_script - Starting processing for user: abobakr.bedda
2025-05-18 21:44:54,276 - INFO - refresh_cache_script - Processing user: abobakr.bedda for data types: ['cms_content']
2025-05-18 21:44:54,277 - INFO - refresh_cache_script - Processing CMS content section for user: abobakr.bedda
2025-05-18 21:44:54,278 - INFO - scraping.cms - Fetching CMS course list for abobakr.bedda from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-18 21:44:54,278 - INFO - refresh_cache_script - Starting processing for user: nour.tantawi
2025-05-18 21:44:54,281 - INFO - refresh_cache_script - Processing user: nour.tantawi for data types: ['cms_content']
2025-05-18 21:44:54,281 - INFO - refresh_cache_script - Processing CMS content section for user: nour.tantawi
2025-05-18 21:44:54,283 - INFO - scraping.cms - Fetching CMS course list for nour.tantawi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-18 21:44:54,283 - INFO - refresh_cache_script - Starting processing for user: mohamed.elsaadi
2025-05-18 21:44:54,284 - INFO - refresh_cache_script - Processing user: mohamed.elsaadi for data types: ['cms_content']
2025-05-18 21:44:54,285 - INFO - refresh_cache_script - Processing CMS content section for user: mohamed.elsaadi
2025-05-18 21:44:54,286 - INFO - scraping.cms - Fetching CMS course list for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/HomePageStn.aspx
2025-05-18 21:44:56,714 - INFO - scraping.cms - Successfully scraped 7 courses for malek.amer.
2025-05-18 21:44:56,715 - INFO - scraping.cms - Successfully scraped 8 courses for abobakr.bedda.
2025-05-18 21:44:56,721 - INFO - refresh_cache_script - User malek.amer has 7 courses. Checking against globally refreshed list.
2025-05-18 21:44:56,723 - INFO - refresh_cache_script - Course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) for user malek.amer needs global refresh.
2025-05-18 21:44:56,724 - INFO - refresh_cache_script - Course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) for user malek.amer needs global refresh.
2025-05-18 21:44:56,724 - INFO - refresh_cache_script - Course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) for user malek.amer needs global refresh.
2025-05-18 21:44:56,725 - INFO - refresh_cache_script - Course '(|EDPT201|) Production Technology (432)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65) for user malek.amer needs global refresh.
2025-05-18 21:44:56,726 - INFO - refresh_cache_script - Course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) for user malek.amer needs global refresh.
2025-05-18 21:44:56,727 - INFO - refresh_cache_script - Course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) for user malek.amer needs global refresh.
2025-05-18 21:44:56,727 - INFO - refresh_cache_script - Course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) for user malek.amer needs global refresh.
2025-05-18 21:44:56,728 - INFO - refresh_cache_script - Awaiting 7 global CMS refresh tasks initiated by malek.amer.
2025-05-18 21:44:56,729 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ELCT201|) Digital Logic Design (79) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65)
2025-05-18 21:44:56,730 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-18 21:44:56,731 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-18 21:44:56,731 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH203|) Mathematics I (17) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65)
2025-05-18 21:44:56,734 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-18 21:44:56,735 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-18 21:44:56,735 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|PHYS202|) Physics II (450) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65)
2025-05-18 21:44:56,739 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-18 21:44:56,739 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-18 21:44:56,740 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|EDPT201|) Production Technology (432) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65)
2025-05-18 21:44:56,743 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-05-18 21:44:56,744 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-05-18 21:44:56,746 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|SM101|) Scientific Methods (A1) (16) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65)
2025-05-18 21:44:56,751 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-18 21:44:56,753 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-18 21:44:56,754 - INFO - refresh_cache_script - User abobakr.bedda has 8 courses. Checking against globally refreshed list.
2025-05-18 21:44:56,756 - INFO - refresh_cache_script - Course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) for user abobakr.bedda needs global refresh.
2025-05-18 21:44:56,757 - INFO - refresh_cache_script - Course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) for user abobakr.bedda needs global refresh.
2025-05-18 21:44:56,757 - INFO - refresh_cache_script - Course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) for user abobakr.bedda needs global refresh.
2025-05-18 21:44:56,758 - INFO - refresh_cache_script - Course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) for user abobakr.bedda needs global refresh.
2025-05-18 21:44:56,759 - INFO - refresh_cache_script - Course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) for user abobakr.bedda needs global refresh.
2025-05-18 21:44:56,760 - INFO - refresh_cache_script - Course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) for user abobakr.bedda needs global refresh.
2025-05-18 21:44:56,760 - INFO - refresh_cache_script - Course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) for user abobakr.bedda needs global refresh.
2025-05-18 21:44:56,761 - INFO - refresh_cache_script - Course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) for user abobakr.bedda needs global refresh.
2025-05-18 21:44:56,762 - INFO - refresh_cache_script - Awaiting 8 global CMS refresh tasks initiated by abobakr.bedda.
2025-05-18 21:44:56,762 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|SM101|) Scientific Methods (A1) (16) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65)
2025-05-18 21:44:56,763 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-18 21:44:56,764 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-18 21:44:56,765 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH203|) Mathematics I (17) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65)
2025-05-18 21:44:56,770 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-18 21:44:56,772 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-18 21:44:56,772 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|PHYS202|) Physics II (450) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65)
2025-05-18 21:44:56,775 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-18 21:44:56,778 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-18 21:44:56,778 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|DE202|) Basic German 2 (33) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65)
2025-05-18 21:44:56,781 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-18 21:44:56,782 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-18 21:44:56,783 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ELCT201|) Digital Logic Design (79) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65)
2025-05-18 21:44:56,787 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-18 21:44:56,787 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-18 21:44:56,925 - INFO - scraping.cms - Successfully scraped 8 courses for seif.elkady.
2025-05-18 21:44:56,928 - INFO - refresh_cache_script - User seif.elkady has 8 courses. Checking against globally refreshed list.
2025-05-18 21:44:56,929 - INFO - refresh_cache_script - Course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) for user seif.elkady needs global refresh.
2025-05-18 21:44:56,930 - INFO - refresh_cache_script - Course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) for user seif.elkady needs global refresh.
2025-05-18 21:44:56,931 - INFO - refresh_cache_script - Course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) for user seif.elkady needs global refresh.
2025-05-18 21:44:56,931 - INFO - refresh_cache_script - Course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) for user seif.elkady needs global refresh.
2025-05-18 21:44:56,934 - INFO - refresh_cache_script - Course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) for user seif.elkady needs global refresh.
2025-05-18 21:44:56,934 - INFO - refresh_cache_script - Course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) for user seif.elkady needs global refresh.
2025-05-18 21:44:56,935 - INFO - refresh_cache_script - Course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) for user seif.elkady needs global refresh.
2025-05-18 21:44:56,935 - INFO - refresh_cache_script - Course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) for user seif.elkady needs global refresh.
2025-05-18 21:44:56,936 - INFO - refresh_cache_script - Awaiting 8 global CMS refresh tasks initiated by seif.elkady.
2025-05-18 21:44:56,936 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|SM101|) Scientific Methods (A1) (16) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65)
2025-05-18 21:44:56,937 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-18 21:44:56,938 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-18 21:44:56,938 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ELCT201|) Digital Logic Design (79) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65)
2025-05-18 21:44:56,939 - INFO - scraping.cms - Successfully scraped 8 courses for ahmed.abd-elhamid.
2025-05-18 21:44:56,944 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-18 21:44:56,945 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-18 21:44:56,951 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN202|) Introduction to Computer Programming (19) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65)
2025-05-18 21:44:56,954 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-18 21:44:56,955 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-18 21:44:56,955 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ENGD301|) Engineering Drawing & Design (49) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65)
2025-05-18 21:44:56,959 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-18 21:44:56,960 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-18 21:44:56,960 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|PHYS202|) Physics II (450) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65)
2025-05-18 21:44:56,966 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-18 21:44:56,967 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-18 21:44:56,969 - INFO - refresh_cache_script - User ahmed.abd-elhamid has 8 courses. Checking against globally refreshed list.
2025-05-18 21:44:56,971 - INFO - refresh_cache_script - Course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) for user ahmed.abd-elhamid needs global refresh.
2025-05-18 21:44:56,972 - INFO - refresh_cache_script - Course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) for user ahmed.abd-elhamid needs global refresh.
2025-05-18 21:44:56,973 - INFO - refresh_cache_script - Course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) for user ahmed.abd-elhamid needs global refresh.
2025-05-18 21:44:56,973 - INFO - refresh_cache_script - Course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) for user ahmed.abd-elhamid needs global refresh.
2025-05-18 21:44:56,974 - INFO - refresh_cache_script - Course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) for user ahmed.abd-elhamid needs global refresh.
2025-05-18 21:44:56,974 - INFO - refresh_cache_script - Course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) for user ahmed.abd-elhamid needs global refresh.
2025-05-18 21:44:56,975 - INFO - refresh_cache_script - Course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) for user ahmed.abd-elhamid needs global refresh.
2025-05-18 21:44:56,976 - INFO - refresh_cache_script - Course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) for user ahmed.abd-elhamid needs global refresh.
2025-05-18 21:44:56,979 - INFO - refresh_cache_script - Awaiting 8 global CMS refresh tasks initiated by ahmed.abd-elhamid.
2025-05-18 21:44:56,980 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|SM101|) Scientific Methods (A1) (16) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65)
2025-05-18 21:44:56,982 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-18 21:44:56,985 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-18 21:44:56,986 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|PHYS202|) Physics II (450) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65)
2025-05-18 21:44:56,988 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-18 21:44:56,989 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-18 21:44:56,990 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH203|) Mathematics I (17) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65)
2025-05-18 21:44:57,000 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-18 21:44:57,001 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-18 21:44:57,001 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|DE202|) Basic German 2 (33) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65)
2025-05-18 21:44:57,005 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-18 21:44:57,007 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-18 21:44:57,007 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN202|) Introduction to Computer Programming (19) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65)
2025-05-18 21:44:57,010 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-18 21:44:57,010 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-18 21:44:57,572 - INFO - scraping.cms - Successfully scraped 8 courses for nour.tantawi.
2025-05-18 21:44:57,573 - INFO - refresh_cache_script - User nour.tantawi has 8 courses. Checking against globally refreshed list.
2025-05-18 21:44:57,573 - INFO - refresh_cache_script - Course '(|RPW401|) Research Paper Writing (A2) (34)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65) for user nour.tantawi needs global refresh.
2025-05-18 21:44:57,574 - INFO - refresh_cache_script - Course '(|BINF405|) Information and Communication Architecture II (486)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65) for user nour.tantawi needs global refresh.
2025-05-18 21:44:57,575 - INFO - refresh_cache_script - Course '(|MATH404|) Math IV (1072)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65) for user nour.tantawi needs global refresh.
2025-05-18 21:44:57,575 - INFO - refresh_cache_script - Course '(|DE404|) Basic German 4 (73)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65) for user nour.tantawi needs global refresh.
2025-05-18 21:44:57,576 - INFO - refresh_cache_script - Course '(|CSEN401|) Computer Programming Lab (402)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65) for user nour.tantawi needs global refresh.
2025-05-18 21:44:57,576 - INFO - refresh_cache_script - Course '(|HROB203|) Human Resources Management for BI (2488)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65) for user nour.tantawi needs global refresh.
2025-05-18 21:44:57,577 - INFO - refresh_cache_script - Course '(|BINF406|) Digital Transformation (2708)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65) for user nour.tantawi needs global refresh.
2025-05-18 21:44:57,578 - INFO - refresh_cache_script - Course '(|CSEN404|) Introduction to Networks (510)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65) for user nour.tantawi needs global refresh.
2025-05-18 21:44:57,578 - INFO - refresh_cache_script - Awaiting 8 global CMS refresh tasks initiated by nour.tantawi.
2025-05-18 21:44:57,579 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|RPW401|) Research Paper Writing (A2) (34) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65)
2025-05-18 21:44:57,580 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-05-18 21:44:57,581 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-05-18 21:44:57,581 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|BINF405|) Information and Communication Architecture II (486) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65)
2025-05-18 21:44:57,584 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-05-18 21:44:57,585 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-05-18 21:44:57,585 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH404|) Math IV (1072) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65)
2025-05-18 21:44:57,589 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-05-18 21:44:57,590 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-05-18 21:44:57,591 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|DE404|) Basic German 4 (73) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65)
2025-05-18 21:44:57,594 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-05-18 21:44:57,595 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-05-18 21:44:57,596 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN401|) Computer Programming Lab (402) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65)
2025-05-18 21:44:57,600 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-05-18 21:44:57,602 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-05-18 21:44:57,661 - INFO - scraping.cms - Successfully scraped 8 courses for malak.mohamedelkady.
2025-05-18 21:44:57,662 - INFO - refresh_cache_script - User malak.mohamedelkady has 8 courses. Checking against globally refreshed list.
2025-05-18 21:44:57,663 - INFO - refresh_cache_script - Course '(|CSEN401|) Computer Programming Lab (402)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65) for user malak.mohamedelkady needs global refresh.
2025-05-18 21:44:57,664 - INFO - refresh_cache_script - Course '(|BINF405|) Information and Communication Architecture II (486)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65) for user malak.mohamedelkady needs global refresh.
2025-05-18 21:44:57,665 - INFO - refresh_cache_script - Course '(|HROB203|) Human Resources Management for BI (2488)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65) for user malak.mohamedelkady needs global refresh.
2025-05-18 21:44:57,666 - INFO - refresh_cache_script - Course '(|CSEN404|) Introduction to Networks (510)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65) for user malak.mohamedelkady needs global refresh.
2025-05-18 21:44:57,666 - INFO - refresh_cache_script - Course '(|BINF406|) Digital Transformation (2708)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65) for user malak.mohamedelkady needs global refresh.
2025-05-18 21:44:57,667 - INFO - refresh_cache_script - Course '(|RPW401|) Research Paper Writing (A2) (34)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65) for user malak.mohamedelkady needs global refresh.
2025-05-18 21:44:57,668 - INFO - refresh_cache_script - Course '(|DE404|) Basic German 4 (73)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65) for user malak.mohamedelkady needs global refresh.
2025-05-18 21:44:57,668 - INFO - refresh_cache_script - Course '(|MATH404|) Math IV (1072)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65) for user malak.mohamedelkady needs global refresh.
2025-05-18 21:44:57,669 - INFO - refresh_cache_script - Awaiting 8 global CMS refresh tasks initiated by malak.mohamedelkady.
2025-05-18 21:44:57,670 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN401|) Computer Programming Lab (402) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65)
2025-05-18 21:44:57,671 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-05-18 21:44:57,671 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-05-18 21:44:57,672 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|BINF405|) Information and Communication Architecture II (486) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65)
2025-05-18 21:44:57,674 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-05-18 21:44:57,675 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-05-18 21:44:57,675 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|HROB203|) Human Resources Management for BI (2488) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65)
2025-05-18 21:44:57,680 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-05-18 21:44:57,680 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-05-18 21:44:57,680 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN404|) Introduction to Networks (510) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65)
2025-05-18 21:44:57,684 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-05-18 21:44:57,685 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-05-18 21:44:57,685 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|BINF406|) Digital Transformation (2708) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65)
2025-05-18 21:44:57,689 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-05-18 21:44:57,690 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-05-18 21:44:59,251 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-18 21:44:59,769 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:44:59,780 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:44:59,966 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-05-18 21:45:00,005 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-18 21:45:00,148 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-18 21:45:00,387 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65
2025-05-18 21:45:00,393 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-18 21:45:00,488 - INFO - scraping.cms - VOD '2 - RPW-  Introduction of the literature review (VoD)' ID '150675_f_1056943' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:00,810 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:00,840 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 11 weeks.
2025-05-18 21:45:01,181 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:01,330 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-18 21:45:01,349 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:01,369 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-05-18 21:45:01,370 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-05-18 21:45:01,371 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-05-18 21:45:01,372 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-05-18 21:45:01,372 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-05-18 21:45:01,373 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-05-18 21:45:01,374 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-05-18 21:45:01,375 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-18 21:45:01,376 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:01,438 - INFO - scraping.cms - VOD '4 - RPW- Literature Review (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-51ed902d-c21f-51d3-1e23-de32b20bb82d' for access URL.
2025-05-18 21:45:01,439 - INFO - scraping.cms - VOD '6 - RPW- Methodology Section (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-607130fb-d676-62ac-7bfb-8e2cd817db36' for access URL.
2025-05-18 21:45:01,440 - INFO - scraping.cms - VOD '2 - RPW- Article Review (VoD)' ID '150675_f_1056934' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:01,469 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 12 weeks.
2025-05-18 21:45:01,529 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 10 weeks.
2025-05-18 21:45:01,591 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-18 21:45:01,597 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 13 weeks.
2025-05-18 21:45:01,614 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 10 weeks.
2025-05-18 21:45:01,668 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-18 21:45:01,872 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-18 21:45:01,938 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-05-18 21:45:01,940 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-05-18 21:45:01,941 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-05-18 21:45:01,941 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-05-18 21:45:01,942 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-05-18 21:45:01,943 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-05-18 21:45:01,944 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-05-18 21:45:01,945 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-18 21:45:01,947 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:01,995 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-18 21:45:02,003 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 12 weeks.
2025-05-18 21:45:02,009 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 11 weeks.
2025-05-18 21:45:02,044 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-05-18 21:45:02,045 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-05-18 21:45:02,046 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-05-18 21:45:02,047 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-05-18 21:45:02,048 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-05-18 21:45:02,050 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-05-18 21:45:02,051 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-05-18 21:45:02,053 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-18 21:45:02,055 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:02,067 - INFO - utils.cache - Set PICKLE cache for key cms_content:36e210f71ca6eaa41355a4dc3b49b7f5 with expiry 3600 seconds
2025-05-18 21:45:02,071 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|CSEN202|) Introduction to Computer Programming (19) (Key: cms_content:36e210f71ca6eaa41355a4dc3b49b7f5)
2025-05-18 21:45:02,073 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ELCT201|) Digital Logic Design (79) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65)
2025-05-18 21:45:02,075 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-18 21:45:02,076 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-18 21:45:02,078 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-18 21:45:02,101 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-18 21:45:02,118 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-05-18 21:45:02,123 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-18 21:45:02,165 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-18 21:45:02,167 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:02,175 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:02,200 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-18 21:45:02,241 - INFO - scraping.cms - VOD '2 - RPW Narrowing Down Steps (Scopus Database) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-bb767829-e552-91a0-eb6a-990d36e54ed2' for access URL.
2025-05-18 21:45:02,244 - INFO - scraping.cms - VOD '4 - RPW Narrowing Down Steps (EBSCOhost Database) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-906e2a09-9ac9-17b7-73d2-947aca7308ee' for access URL.
2025-05-18 21:45:02,246 - INFO - scraping.cms - VOD '4 - RPW- Research Terminology (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-d8fa6938-9caa-7132-215a-425f77ef607e' for access URL.
2025-05-18 21:45:02,249 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65. Found 5 weeks.
2025-05-18 21:45:02,319 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-18 21:45:02,375 - INFO - scraping.cms - Successfully scraped 8 courses for mohamed.elsaadi.
2025-05-18 21:45:02,457 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65. Found 12 weeks.
2025-05-18 21:45:02,617 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-18 21:45:02,625 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-18 21:45:02,626 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:02,632 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-18 21:45:02,641 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-18 21:45:02,642 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:02,869 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-05-18 21:45:02,870 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-05-18 21:45:02,872 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-05-18 21:45:02,873 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:03,005 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-18 21:45:03,006 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:03,070 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-18 21:45:03,126 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-05-18 21:45:03,127 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-05-18 21:45:03,129 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-05-18 21:45:03,130 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:03,194 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65. Found 12 weeks.
2025-05-18 21:45:03,221 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:03,250 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-05-18 21:45:03,251 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-05-18 21:45:03,253 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-05-18 21:45:03,254 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:03,459 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65. Found 14 weeks.
2025-05-18 21:45:03,464 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-18 21:45:03,497 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-05-18 21:45:03,523 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 11 weeks.
2025-05-18 21:45:03,565 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:03,569 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 12 weeks.
2025-05-18 21:45:03,626 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:03,636 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-05-18 21:45:03,656 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-05-18 21:45:03,657 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-05-18 21:45:03,658 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-05-18 21:45:03,658 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-05-18 21:45:03,659 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-05-18 21:45:03,660 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-05-18 21:45:03,660 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-05-18 21:45:03,661 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-18 21:45:03,662 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:03,758 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65. Found 12 weeks.
2025-05-18 21:45:03,809 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-18 21:45:03,825 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-05-18 21:45:03,827 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-05-18 21:45:03,928 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 11 weeks.
2025-05-18 21:45:03,978 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-05-18 21:45:04,135 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65. Found 18 weeks.
2025-05-18 21:45:04,171 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:04,186 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:04,309 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-05-18 21:45:04,363 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:04,366 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-18 21:45:04,368 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:04,392 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:04,395 - INFO - scraping.cms - VOD '2 - Blockchain Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b3790955-857d-ac72-102a-6566cc43a321' for access URL.
2025-05-18 21:45:04,397 - INFO - scraping.cms - VOD '3 - Tutorial 5 VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-58216c6b-b89b-8fcd-d41f-d8c99c564067' for access URL.
2025-05-18 21:45:04,401 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65. Found 11 weeks.
2025-05-18 21:45:04,514 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65
2025-05-18 21:45:04,599 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-05-18 21:45:04,707 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65. Found 14 weeks.
2025-05-18 21:45:04,714 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 13 weeks.
2025-05-18 21:45:04,718 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65. Found 10 weeks.
2025-05-18 21:45:04,737 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65. Found 11 weeks.
2025-05-18 21:45:04,942 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:04,960 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:05,008 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65
2025-05-18 21:45:05,017 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-05-18 21:45:05,018 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-05-18 21:45:05,019 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-05-18 21:45:05,021 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:05,023 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 13 weeks.
2025-05-18 21:45:05,374 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:05,391 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-05-18 21:45:05,543 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:05,663 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-18 21:45:05,664 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:05,668 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-18 21:45:05,792 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-05-18 21:45:05,838 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:05,876 - INFO - utils.cache - Set PICKLE cache for key cms_content:7ce0c432e3ef37a02ca32318f43a43bb with expiry 3600 seconds
2025-05-18 21:45:05,877 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|ELCT201|) Digital Logic Design (79) (Key: cms_content:7ce0c432e3ef37a02ca32318f43a43bb)
2025-05-18 21:45:06,188 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 12 weeks.
2025-05-18 21:45:06,189 - INFO - scraping.cms - VOD '4 - Final Revision - Part 1 (VoD)' ID '150675_f_1067515' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:06,322 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:06,387 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:06,526 - INFO - scraping.cms - VOD '5 - Final Revision: Part-2 (VoD)' ID '150675_f_1067508' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:06,656 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:06,753 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:06,883 - INFO - scraping.cms - VOD '6 - Final Revision: Part-3 (VoD)' ID '150675_f_1067512' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:07,075 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:07,252 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:07,255 - INFO - scraping.cms - VOD '3 - Tutorial 11 (ws 11) (VoD)' ID '150675_f_1069055' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:07,443 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:07,538 - INFO - scraping.cms - VOD '1 - Revision Part I (VoD)' ID '150675_f_1123696' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:07,581 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:07,908 - INFO - scraping.cms - VOD '1 - Lektion 24 Video 1 (VoD)' ID '150675_f_1073495' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:07,915 - INFO - scraping.cms - VOD '3 - Tutorial 10 (ws 10) (VoD)' ID '150675_f_1069054' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:07,998 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:08,241 - INFO - scraping.cms - VOD '2 - Lektion 24 Video 1 Komplett (VoD)' ID '150675_f_1073500' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:08,338 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:08,413 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:08,532 - INFO - scraping.cms - VOD '3 - Tutorial 9 (ws 9) (VoD)' ID '150675_f_1069048' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:08,534 - INFO - utils.cache - Set PICKLE cache for key cms_content:7ce0c432e3ef37a02ca32318f43a43bb with expiry 3600 seconds
2025-05-18 21:45:08,535 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|ELCT201|) Digital Logic Design (79) (Key: cms_content:7ce0c432e3ef37a02ca32318f43a43bb)
2025-05-18 21:45:08,722 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:08,732 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:08,818 - INFO - scraping.cms - VOD '3 - Lektion 24 Video 2 Komplett (VoD)' ID '150675_f_1073502' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:08,839 - INFO - utils.cache - Set PICKLE cache for key cms_content:ec815af072c3ffe92168e7dffd8ef6a6 with expiry 3600 seconds
2025-05-18 21:45:08,840 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|PHYS202|) Physics II (450) (Key: cms_content:ec815af072c3ffe92168e7dffd8ef6a6)
2025-05-18 21:45:08,841 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN202|) Introduction to Computer Programming (19) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65)
2025-05-18 21:45:08,842 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-18 21:45:08,843 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-18 21:45:08,843 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ENGD301|) Engineering Drawing & Design (49) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65)
2025-05-18 21:45:08,846 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-18 21:45:08,847 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-18 21:45:08,848 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|DE202|) Basic German 2 (33) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65)
2025-05-18 21:45:08,852 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-18 21:45:08,853 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-18 21:45:09,062 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:09,126 - INFO - scraping.cms - VOD '3 - Tutorial 8 (ws 8) (VoD)' ID '150675_f_1069045' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:09,146 - INFO - scraping.cms - VOD '1 - Lektion 23 Video 1 (VoD)' ID '150675_f_1073450' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:09,339 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:09,407 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:09,495 - INFO - scraping.cms - VOD '2 - Lektion 23 Video 2 (VoD)' ID '150675_f_1073453' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:09,712 - INFO - utils.cache - Set PICKLE cache for key cms_content:36e210f71ca6eaa41355a4dc3b49b7f5 with expiry 3600 seconds
2025-05-18 21:45:09,712 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-18 21:45:09,713 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|CSEN202|) Introduction to Computer Programming (19) (Key: cms_content:36e210f71ca6eaa41355a4dc3b49b7f5)
2025-05-18 21:45:09,714 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:09,770 - INFO - scraping.cms - VOD '3 - Tutorial 7 (ws 7) (VoD)' ID '150675_f_1069042' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:09,855 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-05-18 21:45:09,869 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:10,091 - INFO - utils.cache - Set PICKLE cache for key cms_content:ec815af072c3ffe92168e7dffd8ef6a6 with expiry 3600 seconds
2025-05-18 21:45:10,092 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|PHYS202|) Physics II (450) (Key: cms_content:ec815af072c3ffe92168e7dffd8ef6a6)
2025-05-18 21:45:10,093 - INFO - scraping.cms - VOD '3 - Lektion 23 Video 3 (VoD)' ID '150675_f_1073456' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:10,127 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-05-18 21:45:10,156 - INFO - scraping.cms - VOD '3 - Tutorial 6 (ws 6) (VoD)' ID '150675_f_1069039' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:10,415 - INFO - scraping.cms - VOD '4 - Lektion 23 Video 1 Komplett (VoD)' ID '150675_f_1073474' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:10,480 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:10,717 - INFO - utils.cache - Set PICKLE cache for key cms_content:d0dd125bb2295e44175ad44ff1ae6a81 with expiry 3600 seconds
2025-05-18 21:45:10,718 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|RPW401|) Research Paper Writing (A2) (34) (Key: cms_content:d0dd125bb2295e44175ad44ff1ae6a81)
2025-05-18 21:45:10,719 - INFO - refresh_cache_script - User mohamed.elsaadi has 8 courses. Checking against globally refreshed list.
2025-05-18 21:45:10,720 - INFO - refresh_cache_script - Course '(|SM101|) Scientific Methods (A1) (16)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65) for user mohamed.elsaadi needs global refresh.
2025-05-18 21:45:10,721 - INFO - refresh_cache_script - Course '(|PHYS202|) Physics II (450)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65) for user mohamed.elsaadi needs global refresh.
2025-05-18 21:45:10,721 - INFO - refresh_cache_script - Course '(|ELCT201|) Digital Logic Design (79)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65) for user mohamed.elsaadi needs global refresh.
2025-05-18 21:45:10,722 - INFO - refresh_cache_script - Course '(|DE202|) Basic German 2 (33)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65) for user mohamed.elsaadi needs global refresh.
2025-05-18 21:45:10,723 - INFO - refresh_cache_script - Course '(|CSEN202|) Introduction to Computer Programming (19)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65) for user mohamed.elsaadi needs global refresh.
2025-05-18 21:45:10,724 - INFO - scraping.cms - VOD '4 - Midterm Revision (VoD)' ID '150675_f_1071230' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:10,725 - INFO - refresh_cache_script - Course '(|MATH203|) Mathematics I (17)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65) for user mohamed.elsaadi needs global refresh.
2025-05-18 21:45:10,726 - INFO - refresh_cache_script - Course '(|ENGD301|) Engineering Drawing & Design (49)' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65) for user mohamed.elsaadi needs global refresh.
2025-05-18 21:45:10,728 - INFO - refresh_cache_script - Course 'Engineering - 2nd Orientation course' (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65) for user mohamed.elsaadi needs global refresh.
2025-05-18 21:45:10,728 - INFO - refresh_cache_script - Awaiting 8 global CMS refresh tasks initiated by mohamed.elsaadi.
2025-05-18 21:45:10,757 - INFO - scraping.cms - VOD '5 - Lektion 23 Video 2 Komplett (VoD)' ID '150675_f_1073481' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:10,761 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:10,973 - INFO - utils.cache - Set PICKLE cache for key cms_content:2c5a7598a5a727af380c8053ec0cd47d with expiry 3600 seconds
2025-05-18 21:45:10,974 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|BINF405|) Information and Communication Architecture II (486) (Key: cms_content:2c5a7598a5a727af380c8053ec0cd47d)
2025-05-18 21:45:11,104 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:11,282 - INFO - scraping.cms - VOD '3 - Tutorial 5 (ws 5) (VoD)' ID '150675_f_1071247' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:11,305 - INFO - scraping.cms - VOD '6 - Lektion 23 Video 3 Komplett (VoD)' ID '150675_f_1073486' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:11,416 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:11,651 - INFO - scraping.cms - VOD '7 - Lektion 23 Video 4 Komplett (VoD)' ID '150675_f_1073485' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:11,767 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-18 21:45:11,768 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:11,858 - INFO - scraping.cms - VOD '3 - Tutorial 4 (ws 4) (VoD)' ID '150675_f_1067374' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:12,063 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-05-18 21:45:12,066 - INFO - utils.cache - Set PICKLE cache for key cms_content:a65dad15c3d92e609d370a8134a22480 with expiry 3600 seconds
2025-05-18 21:45:12,067 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|MATH203|) Mathematics I (17) (Key: cms_content:a65dad15c3d92e609d370a8134a22480)
2025-05-18 21:45:12,190 - INFO - scraping.cms - VOD '3 - Tutorial 3 (ws 3) (VoD)' ID '150675_f_1066977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:12,219 - INFO - scraping.cms - VOD '1 - Lektion 22 Video 1 (VoD)' ID '150675_f_1073442' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:12,313 - INFO - utils.cache - Set PICKLE cache for key cms_content:2c5a7598a5a727af380c8053ec0cd47d with expiry 3600 seconds
2025-05-18 21:45:12,314 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|BINF405|) Information and Communication Architecture II (486) (Key: cms_content:2c5a7598a5a727af380c8053ec0cd47d)
2025-05-18 21:45:12,477 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:12,516 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65. Found 16 weeks.
2025-05-18 21:45:12,610 - INFO - utils.cache - Set PICKLE cache for key cms_content:fd3d5bccd7619e6b8cbf5a5d1463d0aa with expiry 3600 seconds
2025-05-18 21:45:12,610 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|CSEN401|) Computer Programming Lab (402) (Key: cms_content:fd3d5bccd7619e6b8cbf5a5d1463d0aa)
2025-05-18 21:45:12,760 - INFO - scraping.cms - VOD '2 - Lektion 22 Video 2 (VoD)' ID '150675_f_1073444' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:12,860 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:13,087 - INFO - scraping.cms - VOD '3 - Lektion 22 complete video 1 (VoD)' ID '150675_f_1073448' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:13,188 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:13,412 - INFO - utils.cache - Set PICKLE cache for key cms_content:70d9362c5579d89129098a7d17df3291 with expiry 3600 seconds
2025-05-18 21:45:13,413 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|SM101|) Scientific Methods (A1) (16) (Key: cms_content:70d9362c5579d89129098a7d17df3291)
2025-05-18 21:45:13,420 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 10 weeks.
2025-05-18 21:45:13,507 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-18 21:45:13,508 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:13,542 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-18 21:45:13,649 - INFO - scraping.cms - VOD '4 - Lektion 22 complete video 2 (VoD)' ID '150675_f_1101615' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:13,778 - INFO - utils.cache - Set PICKLE cache for key cms_content:ec815af072c3ffe92168e7dffd8ef6a6 with expiry 3600 seconds
2025-05-18 21:45:13,779 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|PHYS202|) Physics II (450) (Key: cms_content:ec815af072c3ffe92168e7dffd8ef6a6)
2025-05-18 21:45:13,982 - INFO - scraping.cms - VOD '1 - Unit 21 Part 1 (VoD)' ID '150675_f_1063834' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:14,044 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-18 21:45:14,101 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:14,320 - INFO - scraping.cms - VOD '2 - Unit 21 Part 2 (VoD)' ID '150675_f_1067572' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:14,417 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:14,655 - INFO - scraping.cms - VOD '3 - Unit 21 (Complete Unit: Part 1) (VoD)' ID '150675_f_1094812' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:14,874 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65. Found 18 weeks.
2025-05-18 21:45:14,985 - INFO - scraping.cms - VOD '4 - Unit 21 (Complete Unit: Part 2) (VoD)' ID '150675_f_1094820' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:15,000 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:15,347 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:15,508 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-18 21:45:15,553 - INFO - utils.cache - Set PICKLE cache for key cms_content:7ce0c432e3ef37a02ca32318f43a43bb with expiry 3600 seconds
2025-05-18 21:45:15,554 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|ELCT201|) Digital Logic Design (79) (Key: cms_content:7ce0c432e3ef37a02ca32318f43a43bb)
2025-05-18 21:45:15,632 - INFO - scraping.cms - VOD '5 - Unit 21 (Complete Unit: Part 3) (VoD)' ID '150675_f_1094828' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:15,664 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:15,814 - INFO - utils.cache - Set PICKLE cache for key cms_content:9a92aa6c600a4f2afd5779272fb47030 with expiry 3600 seconds
2025-05-18 21:45:15,815 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|HROB203|) Human Resources Management for BI (2488) (Key: cms_content:9a92aa6c600a4f2afd5779272fb47030)
2025-05-18 21:45:16,002 - INFO - scraping.cms - VOD '1 - Unit 20 (VoD)' ID '150675_f_1063828' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:16,071 - INFO - utils.cache - Set PICKLE cache for key cms_content:70d9362c5579d89129098a7d17df3291 with expiry 3600 seconds
2025-05-18 21:45:16,072 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|SM101|) Scientific Methods (A1) (16) (Key: cms_content:70d9362c5579d89129098a7d17df3291)
2025-05-18 21:45:16,277 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:16,362 - INFO - scraping.cms - VOD '2 - Unit 20 (Complete Unit) (VoD)' ID '150675_f_1092097' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:16,411 - INFO - utils.cache - Set PICKLE cache for key cms_content:ec815af072c3ffe92168e7dffd8ef6a6 with expiry 3600 seconds
2025-05-18 21:45:16,412 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|PHYS202|) Physics II (450) (Key: cms_content:ec815af072c3ffe92168e7dffd8ef6a6)
2025-05-18 21:45:16,659 - INFO - utils.cache - Set PICKLE cache for key cms_content:70d9362c5579d89129098a7d17df3291 with expiry 3600 seconds
2025-05-18 21:45:16,659 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|SM101|) Scientific Methods (A1) (16) (Key: cms_content:70d9362c5579d89129098a7d17df3291)
2025-05-18 21:45:16,907 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:16,910 - INFO - utils.cache - Set PICKLE cache for key cms_content:82542a10b678a487bf3d8d0052c3f6f5 with expiry 3600 seconds
2025-05-18 21:45:16,910 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|ENGD301|) Engineering Drawing & Design (49) (Key: cms_content:82542a10b678a487bf3d8d0052c3f6f5)
2025-05-18 21:45:17,001 - INFO - scraping.cms - VOD '3 - K19 - V1 (VoD)' ID '150675_f_1062891' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:17,164 - INFO - utils.cache - Set PICKLE cache for key cms_content:e379899422f6737123ba21e692610eed with expiry 3600 seconds
2025-05-18 21:45:17,165 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|BINF406|) Digital Transformation (2708) (Key: cms_content:e379899422f6737123ba21e692610eed)
2025-05-18 21:45:17,282 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:17,317 - INFO - scraping.cms - VOD '4 - Unit 19 Complete Unit (Part1) (VoD)' ID '150675_f_1066246' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:17,509 - INFO - utils.cache - Set PICKLE cache for key cms_content:fd3d5bccd7619e6b8cbf5a5d1463d0aa with expiry 3600 seconds
2025-05-18 21:45:17,510 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|CSEN401|) Computer Programming Lab (402) (Key: cms_content:fd3d5bccd7619e6b8cbf5a5d1463d0aa)
2025-05-18 21:45:17,618 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:17,665 - INFO - scraping.cms - VOD '5 - Unit 19 Complete Unit (Part 2) (VoD)' ID '150675_f_1066249' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:18,191 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:18,282 - INFO - scraping.cms - VOD '1 - Revision 1 (VoD)' ID '150675_f_1124540' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:18,611 - INFO - scraping.cms - VOD '2 - Revision 2 (VoD)' ID '150675_f_1124541' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:18,775 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:18,938 - INFO - scraping.cms - VOD '4 - Model test DE3 AUDIO (VoD)' ID '150675_f_1124235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:19,132 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:19,265 - INFO - scraping.cms - VOD '1 - U18-V1 Blended (VoD)' ID '150675_f_1090464' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:19,448 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:19,594 - INFO - utils.cache - Set PICKLE cache for key cms_content:a65dad15c3d92e609d370a8134a22480 with expiry 3600 seconds
2025-05-18 21:45:19,595 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|MATH203|) Mathematics I (17) (Key: cms_content:a65dad15c3d92e609d370a8134a22480)
2025-05-18 21:45:19,612 - INFO - scraping.cms - VOD '2 - U18-V2 Blended (VoD)' ID '150675_f_1090082' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:19,899 - INFO - scraping.cms - VOD '3 - U18-V1 Complete (VoD)' ID '150675_f_1090084' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:20,064 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-18 21:45:20,065 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:20,216 - INFO - utils.cache - Set PICKLE cache for key cms_content:3b7810d96cf07cd7afe4593d27ed4c3e with expiry 3600 seconds
2025-05-18 21:45:20,216 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|EDPT201|) Production Technology (432) (Key: cms_content:3b7810d96cf07cd7afe4593d27ed4c3e)
2025-05-18 21:45:20,225 - INFO - scraping.cms - VOD '4 - U18-V2 Complete (VoD)' ID '150675_f_1090083' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:20,657 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-05-18 21:45:20,798 - INFO - scraping.cms - VOD '1 - U17- V1 Blended (VoD)' ID '150675_f_1090058' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:21,078 - INFO - utils.cache - Set PICKLE cache for key cms_content:a0c90cd039e29094137c8bf4387596bb with expiry 3600 seconds
2025-05-18 21:45:21,078 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|CSEN404|) Introduction to Networks (510) (Key: cms_content:a0c90cd039e29094137c8bf4387596bb)
2025-05-18 21:45:21,103 - INFO - scraping.cms - VOD '2 - U17-V2 Blended (VoD)' ID '150675_f_1090062' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:21,447 - INFO - scraping.cms - VOD '3 - U17-V3 Blended (VoD)' ID '150675_f_1090064' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:21,980 - INFO - utils.cache - Set PICKLE cache for key cms_content:a65dad15c3d92e609d370a8134a22480 with expiry 3600 seconds
2025-05-18 21:45:21,981 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|MATH203|) Mathematics I (17) (Key: cms_content:a65dad15c3d92e609d370a8134a22480)
2025-05-18 21:45:22,046 - INFO - scraping.cms - VOD '4 - U17 - V1 Complete (VoD)' ID '150675_f_1090063' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:22,242 - INFO - utils.cache - Set PICKLE cache for key cms_content:70d9362c5579d89129098a7d17df3291 with expiry 3600 seconds
2025-05-18 21:45:22,243 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|SM101|) Scientific Methods (A1) (16) (Key: cms_content:70d9362c5579d89129098a7d17df3291)
2025-05-18 21:45:22,351 - INFO - scraping.cms - VOD '5 - U17 - V2 Complete (VoD)' ID '150675_f_1090067' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:22,703 - INFO - scraping.cms - VOD '6 - U17-V3 Complete (VoD)' ID '150675_f_1090071' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:23,096 - INFO - scraping.cms - VOD '1 - U16- V1 Blended (VoD)' ID '150675_f_1090053' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:23,523 - INFO - scraping.cms - VOD '2 - U16- V1 complete (VoD)' ID '150675_f_1090057' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:24,139 - INFO - scraping.cms - VOD '3 - U16-V2 complete (VoD)' ID '150675_f_1090462' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:24,752 - INFO - scraping.cms - VOD '1 - U13-complete Video1 (VoD)' ID '150675_f_1065580' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:25,077 - INFO - scraping.cms - VOD '2 - U13- complete Video2 (VoD)' ID '150675_f_1065582' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:25,679 - INFO - scraping.cms - VOD '1 - U15-V1 (VoD)' ID '150675_f_1065068' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:26,069 - INFO - utils.cache - Set PICKLE cache for key cms_content:7ce0c432e3ef37a02ca32318f43a43bb with expiry 3600 seconds
2025-05-18 21:45:26,070 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|ELCT201|) Digital Logic Design (79) (Key: cms_content:7ce0c432e3ef37a02ca32318f43a43bb)
2025-05-18 21:45:26,071 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH203|) Mathematics I (17) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65)
2025-05-18 21:45:26,073 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-18 21:45:26,074 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-18 21:45:26,075 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN202|) Introduction to Computer Programming (19) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65)
2025-05-18 21:45:26,077 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-18 21:45:26,077 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-18 21:45:26,078 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|HROB203|) Human Resources Management for BI (2488) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65)
2025-05-18 21:45:26,081 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-05-18 21:45:26,082 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-05-18 21:45:26,082 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|SM101|) Scientific Methods (A1) (16) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65)
2025-05-18 21:45:26,086 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-18 21:45:26,087 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-18 21:45:26,087 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|PHYS202|) Physics II (450) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65)
2025-05-18 21:45:26,091 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-18 21:45:26,093 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-18 21:45:26,093 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ELCT201|) Digital Logic Design (79) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65)
2025-05-18 21:45:26,096 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-18 21:45:26,096 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-18 21:45:26,097 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|DE202|) Basic German 2 (33) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65)
2025-05-18 21:45:26,100 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-18 21:45:26,101 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-18 21:45:26,102 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN202|) Introduction to Computer Programming (19) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65)
2025-05-18 21:45:26,105 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-18 21:45:26,106 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-18 21:45:26,107 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|RPW401|) Research Paper Writing (A2) (34) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65)
2025-05-18 21:45:26,110 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-05-18 21:45:26,110 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-05-18 21:45:26,111 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for Engineering - 2nd Orientation course (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65)
2025-05-18 21:45:26,114 - INFO - scraping.cms - Fetching CMS course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-18 21:45:26,116 - INFO - scraping.cms - Fetching CMS course announcements for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-18 21:45:26,116 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|BINF406|) Digital Transformation (2708) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65)
2025-05-18 21:45:26,119 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-05-18 21:45:26,120 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-05-18 21:45:26,120 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|CSEN404|) Introduction to Networks (510) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65)
2025-05-18 21:45:26,124 - INFO - scraping.cms - Fetching CMS course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-05-18 21:45:26,125 - INFO - scraping.cms - Fetching CMS course announcements for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-05-18 21:45:26,126 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ENGD301|) Engineering Drawing & Design (49) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65)
2025-05-18 21:45:26,128 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-18 21:45:26,129 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-18 21:45:26,129 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for Engineering - 2nd Orientation course (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65)
2025-05-18 21:45:26,133 - INFO - scraping.cms - Fetching CMS course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-18 21:45:26,135 - INFO - scraping.cms - Fetching CMS course announcements for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-18 21:45:26,135 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|DE404|) Basic German 4 (73) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65)
2025-05-18 21:45:26,138 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-05-18 21:45:26,140 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-05-18 21:45:26,140 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for Engineering - 2nd Orientation course (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65)
2025-05-18 21:45:26,142 - INFO - scraping.cms - Fetching CMS course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-18 21:45:26,144 - INFO - scraping.cms - Fetching CMS course announcements for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-18 21:45:26,144 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for Engineering - 2nd Orientation course (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65)
2025-05-18 21:45:26,147 - INFO - scraping.cms - Fetching CMS course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-18 21:45:26,148 - INFO - scraping.cms - Fetching CMS course announcements for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-18 21:45:26,149 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH404|) Math IV (1072) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65)
2025-05-18 21:45:26,151 - INFO - scraping.cms - Fetching CMS course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-05-18 21:45:26,152 - INFO - scraping.cms - Fetching CMS course announcements for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-05-18 21:45:26,287 - INFO - scraping.cms - VOD '2 - U16-V1 (VoD)' ID '150675_f_1065069' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:26,918 - INFO - scraping.cms - VOD '3 - U15 V1 Complete (VoD)' ID '150675_f_1141997' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:26,937 - INFO - utils.cache - Set PICKLE cache for key cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc with expiry 3600 seconds
2025-05-18 21:45:26,938 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|DE202|) Basic German 2 (33) (Key: cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc)
2025-05-18 21:45:26,949 - INFO - scraping.cms - Finished parsing course content for malek.amer from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-05-18 21:45:27,038 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-18 21:45:27,233 - INFO - utils.cache - Set PICKLE cache for key cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc with expiry 3600 seconds
2025-05-18 21:45:27,233 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|DE202|) Basic German 2 (33) (Key: cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc)
2025-05-18 21:45:27,292 - INFO - scraping.cms - VOD '1 - U14 - V1 (VoD)' ID '150675_f_1063454' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:27,647 - INFO - utils.cache - Set PICKLE cache for key cms_content:24ac4b2ef071b508afcbda016918c8cc with expiry 3600 seconds
2025-05-18 21:45:27,648 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|MATH404|) Math IV (1072) (Key: cms_content:24ac4b2ef071b508afcbda016918c8cc)
2025-05-18 21:45:27,678 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-05-18 21:45:27,864 - INFO - scraping.cms - VOD '2 - U14- V2 (VoD)' ID '150675_f_1065039' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:27,992 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65
2025-05-18 21:45:27,998 - INFO - utils.cache - Set PICKLE cache for key cms_content:36e210f71ca6eaa41355a4dc3b49b7f5 with expiry 3600 seconds
2025-05-18 21:45:27,999 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|CSEN202|) Introduction to Computer Programming (19) (Key: cms_content:36e210f71ca6eaa41355a4dc3b49b7f5)
2025-05-18 21:45:28,179 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-18 21:45:28,238 - INFO - scraping.cms - VOD '3 - U14-V3 (VoD)' ID '150675_f_1065060' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:28,346 - INFO - utils.cache - Set PICKLE cache for key cms_content:82542a10b678a487bf3d8d0052c3f6f5 with expiry 3600 seconds
2025-05-18 21:45:28,347 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|ENGD301|) Engineering Drawing & Design (49) (Key: cms_content:82542a10b678a487bf3d8d0052c3f6f5)
2025-05-18 21:45:28,603 - INFO - utils.cache - Set PICKLE cache for key cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc with expiry 3600 seconds
2025-05-18 21:45:28,604 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|DE202|) Basic German 2 (33) (Key: cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc)
2025-05-18 21:45:28,821 - INFO - utils.cache - Set PICKLE cache for key cms_content:075b4828d3fee66f0d27d4a0edfb1696 with expiry 3600 seconds
2025-05-18 21:45:28,822 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for Engineering - 2nd Orientation course (Key: cms_content:075b4828d3fee66f0d27d4a0edfb1696)
2025-05-18 21:45:28,823 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65 (initiated by malek.amer) successful.
2025-05-18 21:45:28,824 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65 (initiated by malek.amer) successful.
2025-05-18 21:45:28,825 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65 (initiated by malek.amer) successful.
2025-05-18 21:45:28,825 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=432&sid=65 (initiated by malek.amer) successful.
2025-05-18 21:45:28,826 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65 (initiated by malek.amer) successful.
2025-05-18 21:45:28,827 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65 (initiated by malek.amer) successful.
2025-05-18 21:45:28,827 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65 (initiated by malek.amer) successful.
2025-05-18 21:45:28,828 - INFO - refresh_cache_script - CMS Content processing summary for user malek.amer: Processed for user malek.amer: Globally Updated Now=7, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-05-18 21:45:28,829 - INFO - refresh_cache_script - Finished processing for user: malek.amer
2025-05-18 21:45:28,845 - INFO - scraping.cms - VOD '4 - U14-V4 (VoD)' ID '150675_f_1065058' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:29,005 - INFO - scraping.cms - VOD '2 - Blockchain Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b3790955-857d-ac72-102a-6566cc43a321' for access URL.
2025-05-18 21:45:29,007 - INFO - scraping.cms - VOD '3 - Tutorial 5 VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-58216c6b-b89b-8fcd-d41f-d8c99c564067' for access URL.
2025-05-18 21:45:29,009 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65. Found 11 weeks.
2025-05-18 21:45:29,010 - INFO - scraping.cms - VOD '2 - RPW-  Introduction of the literature review (VoD)' ID '150675_f_1056943' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:29,190 - INFO - scraping.cms - VOD '5 - U14 Complete Video (VoD)' ID '150675_f_1141802' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:29,363 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65
2025-05-18 21:45:29,501 - INFO - scraping.cms - VOD '1 - Fallacies (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-9b7d5593-97cd-b360-60a0-b0a8348d8cc7' for access URL.
2025-05-18 21:45:29,502 - INFO - scraping.cms - VOD '3 - Dettol Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f718d12d-af3c-b6e6-5114-fb89029ddbc6' for access URL.
2025-05-18 21:45:29,503 - INFO - scraping.cms - VOD '4 - Persil Ad/ Bill Nye (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-26cff063-e85a-72e3-94eb-c64d535b0044' for access URL.
2025-05-18 21:45:29,504 - INFO - scraping.cms - VOD '5 - Olay Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-0288f6e7-d280-b90f-5006-b01ed6855348' for access URL.
2025-05-18 21:45:29,505 - INFO - scraping.cms - VOD '6 - Persil Ad/ Ghada Adel (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1578b400-19f7-1973-18fc-8e1f2c58d896' for access URL.
2025-05-18 21:45:29,506 - INFO - scraping.cms - VOD '7 - Vodafone Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-f2e22cb3-de1d-2c5f-9f8e-bc107ab5edff' for access URL.
2025-05-18 21:45:29,506 - INFO - scraping.cms - VOD '8 - Fair & Lovely Ad (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-ddb26793-3784-115c-447e-74b6c6b4ca80' for access URL.
2025-05-18 21:45:29,508 - INFO - scraping.cms - VOD '1 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-18 21:45:29,509 - INFO - scraping.cms - VOD '4 - Report Writing VoD (VoD)' ID '150675_f_1165078' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:29,539 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65
2025-05-18 21:45:29,587 - INFO - scraping.cms - VOD '4 - RPW- Literature Review (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-51ed902d-c21f-51d3-1e23-de32b20bb82d' for access URL.
2025-05-18 21:45:29,588 - INFO - scraping.cms - VOD '6 - RPW- Methodology Section (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-607130fb-d676-62ac-7bfb-8e2cd817db36' for access URL.
2025-05-18 21:45:29,590 - INFO - scraping.cms - VOD '2 - RPW- Article Review (VoD)' ID '150675_f_1056934' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:29,595 - INFO - scraping.cms - VOD '6 - U14 Complete V2 (VoD)' ID '150675_f_1141799' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:29,690 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:29,845 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65. Found 12 weeks.
2025-05-18 21:45:29,897 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 10 weeks.
2025-05-18 21:45:29,927 - INFO - scraping.cms - VOD '1 - K13- V1 (VoD)' ID '150675_f_1062824' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:30,078 - INFO - scraping.cms - VOD '6 - Assumptions/ What is a good argument? (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-68b74154-3af2-8c70-c52e-4cce331ec9b1' for access URL.
2025-05-18 21:45:30,079 - INFO - scraping.cms - VOD '2 - Inferences (VoD)' ID '150675_f_1057807' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:30,106 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-05-18 21:45:30,184 - INFO - utils.cache - Set PICKLE cache for key cms_content:e379899422f6737123ba21e692610eed with expiry 3600 seconds
2025-05-18 21:45:30,185 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|BINF406|) Digital Transformation (2708) (Key: cms_content:e379899422f6737123ba21e692610eed)
2025-05-18 21:45:30,196 - INFO - scraping.cms - VOD '2 - RPW Narrowing Down Steps (Scopus Database) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-bb767829-e552-91a0-eb6a-990d36e54ed2' for access URL.
2025-05-18 21:45:30,197 - INFO - scraping.cms - VOD '4 - RPW Narrowing Down Steps (EBSCOhost Database) (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-906e2a09-9ac9-17b7-73d2-947aca7308ee' for access URL.
2025-05-18 21:45:30,198 - INFO - scraping.cms - VOD '4 - RPW- Research Terminology (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-d8fa6938-9caa-7132-215a-425f77ef607e' for access URL.
2025-05-18 21:45:30,200 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65. Found 5 weeks.
2025-05-18 21:45:30,264 - INFO - scraping.cms - Finished parsing course content for seif.elkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 13 weeks.
2025-05-18 21:45:30,390 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-18 21:45:30,503 - INFO - scraping.cms - VOD '2 - K13 - V2 (VoD)' ID '150675_f_1062876' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:30,551 - INFO - utils.cache - Set PICKLE cache for key cms_content:36e210f71ca6eaa41355a4dc3b49b7f5 with expiry 3600 seconds
2025-05-18 21:45:30,553 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|CSEN202|) Introduction to Computer Programming (19) (Key: cms_content:36e210f71ca6eaa41355a4dc3b49b7f5)
2025-05-18 21:45:30,564 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65. Found 11 weeks.
2025-05-18 21:45:30,597 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-18 21:45:30,605 - INFO - scraping.cms - VOD '1 - Facts vs. Opinions/ Connotations VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-87490c0e-1e5c-2664-65e6-674b26b027de' for access URL.
2025-05-18 21:45:30,606 - INFO - scraping.cms - VOD '1 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-4cb37432-ab7b-7e6d-d850-b39cc7f82327' for access URL.
2025-05-18 21:45:30,608 - INFO - scraping.cms - VOD '2 - Questionnaire VoD (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-fa780854-884d-572a-4540-fa90fd63d23f' for access URL.
2025-05-18 21:45:30,609 - INFO - scraping.cms - VOD '5 - Problem Solving (VoD)' ID '150675_f_1156979' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:30,807 - INFO - utils.cache - Set PICKLE cache for key cms_content:d0dd125bb2295e44175ad44ff1ae6a81 with expiry 3600 seconds
2025-05-18 21:45:30,808 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|RPW401|) Research Paper Writing (A2) (34) (Key: cms_content:d0dd125bb2295e44175ad44ff1ae6a81)
2025-05-18 21:45:30,849 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-18 21:45:30,889 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:30,926 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65
2025-05-18 21:45:30,958 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65. Found 9 weeks.
2025-05-18 21:45:31,022 - INFO - utils.cache - Set PICKLE cache for key cms_content:075b4828d3fee66f0d27d4a0edfb1696 with expiry 3600 seconds
2025-05-18 21:45:31,023 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for Engineering - 2nd Orientation course (Key: cms_content:075b4828d3fee66f0d27d4a0edfb1696)
2025-05-18 21:45:31,075 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65
2025-05-18 21:45:31,318 - INFO - scraping.cms - VOD '4 - Final Revision - Part 1 (VoD)' ID '150675_f_1067515' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:31,416 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65
2025-05-18 21:45:31,445 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:31,530 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:31,587 - INFO - scraping.cms - VOD '1 - VOD Lecture: Multiplier, Comparator Parity Checker (VoD)' ID '150675_f_1073578' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:31,627 - INFO - scraping.cms - VOD '5 - Final Revision: Part-2 (VoD)' ID '150675_f_1067508' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:31,689 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65. Found 11 weeks.
2025-05-18 21:45:31,847 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:32,014 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:32,020 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65. Found 10 weeks.
2025-05-18 21:45:32,180 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65. Found 12 weeks.
2025-05-18 21:45:32,214 - INFO - scraping.cms - VOD '6 - Final Revision: Part-3 (VoD)' ID '150675_f_1067512' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:32,346 - INFO - utils.cache - Set PICKLE cache for key cms_content:a65dad15c3d92e609d370a8134a22480 with expiry 3600 seconds
2025-05-18 21:45:32,347 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|MATH203|) Mathematics I (17) (Key: cms_content:a65dad15c3d92e609d370a8134a22480)
2025-05-18 21:45:32,417 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:32,420 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65
2025-05-18 21:45:32,533 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65
2025-05-18 21:45:32,559 - INFO - scraping.cms - VOD '3 - Tutorial 11 (ws 11) (VoD)' ID '150675_f_1069055' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:32,563 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65
2025-05-18 21:45:32,564 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-18 21:45:32,566 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:32,623 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65
2025-05-18 21:45:32,644 - INFO - utils.cache - Set PICKLE cache for key cms_content:70d9362c5579d89129098a7d17df3291 with expiry 3600 seconds
2025-05-18 21:45:32,644 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|SM101|) Scientific Methods (A1) (16) (Key: cms_content:70d9362c5579d89129098a7d17df3291)
2025-05-18 21:45:32,645 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|MATH203|) Mathematics I (17) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65)
2025-05-18 21:45:32,646 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-18 21:45:32,647 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-18 21:45:32,754 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-18 21:45:32,756 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:32,920 - INFO - scraping.cms - VOD '3 - Tutorial 10 (ws 10) (VoD)' ID '150675_f_1069054' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:32,976 - INFO - utils.cache - Set PICKLE cache for key cms_content:9a92aa6c600a4f2afd5779272fb47030 with expiry 3600 seconds
2025-05-18 21:45:32,977 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|HROB203|) Human Resources Management for BI (2488) (Key: cms_content:9a92aa6c600a4f2afd5779272fb47030)
2025-05-18 21:45:33,113 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:33,116 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:33,255 - INFO - scraping.cms - VOD '3 - Tutorial 9 (ws 9) (VoD)' ID '150675_f_1069048' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:33,430 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:33,592 - INFO - scraping.cms - VOD '3 - Tutorial 8 (ws 8) (VoD)' ID '150675_f_1069045' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:33,615 - INFO - utils.cache - Set PICKLE cache for key cms_content:a0c90cd039e29094137c8bf4387596bb with expiry 3600 seconds
2025-05-18 21:45:33,616 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|CSEN404|) Introduction to Networks (510) (Key: cms_content:a0c90cd039e29094137c8bf4387596bb)
2025-05-18 21:45:33,748 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:34,017 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:34,098 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:34,194 - INFO - scraping.cms - VOD '3 - Tutorial 7 (ws 7) (VoD)' ID '150675_f_1069042' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:34,265 - INFO - utils.cache - Set PICKLE cache for key cms_content:36e210f71ca6eaa41355a4dc3b49b7f5 with expiry 3600 seconds
2025-05-18 21:45:34,266 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|CSEN202|) Introduction to Computer Programming (19) (Key: cms_content:36e210f71ca6eaa41355a4dc3b49b7f5)
2025-05-18 21:45:34,517 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:34,589 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:34,887 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-18 21:45:34,918 - INFO - scraping.cms - VOD '3 - Tutorial 6 (ws 6) (VoD)' ID '150675_f_1069039' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:34,926 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:34,999 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:35,244 - INFO - scraping.cms - VOD '4 - Midterm Revision (VoD)' ID '150675_f_1071230' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:35,337 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:35,551 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:35,701 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:35,831 - INFO - scraping.cms - Finished parsing course content for ahmed.abd-elhamid from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65. Found 18 weeks.
2025-05-18 21:45:35,879 - INFO - scraping.cms - VOD '3 - Tutorial 5 (ws 5) (VoD)' ID '150675_f_1071247' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:35,976 - INFO - scraping.cms - Finished parsing course content for abobakr.bedda from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-05-18 21:45:36,038 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:36,150 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:36,153 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-18 21:45:36,175 - INFO - utils.cache - Set PICKLE cache for key cms_content:7ce0c432e3ef37a02ca32318f43a43bb with expiry 3600 seconds
2025-05-18 21:45:36,176 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|ELCT201|) Digital Logic Design (79) (Key: cms_content:7ce0c432e3ef37a02ca32318f43a43bb)
2025-05-18 21:45:36,177 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65 (initiated by seif.elkady) successful.
2025-05-18 21:45:36,177 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65 (initiated by seif.elkady) successful.
2025-05-18 21:45:36,178 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65 (initiated by seif.elkady) successful.
2025-05-18 21:45:36,178 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65 (initiated by seif.elkady) successful.
2025-05-18 21:45:36,179 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65 (initiated by seif.elkady) successful.
2025-05-18 21:45:36,180 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65 (initiated by seif.elkady) successful.
2025-05-18 21:45:36,180 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65 (initiated by seif.elkady) successful.
2025-05-18 21:45:36,181 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65 (initiated by seif.elkady) successful.
2025-05-18 21:45:36,181 - INFO - refresh_cache_script - CMS Content processing summary for user seif.elkady: Processed for user seif.elkady: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-05-18 21:45:36,182 - INFO - refresh_cache_script - Finished processing for user: seif.elkady
2025-05-18 21:45:36,226 - INFO - scraping.cms - VOD '3 - Tutorial 4 (ws 4) (VoD)' ID '150675_f_1067374' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:36,536 - INFO - utils.cache - Set PICKLE cache for key cms_content:ec815af072c3ffe92168e7dffd8ef6a6 with expiry 3600 seconds
2025-05-18 21:45:36,537 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|PHYS202|) Physics II (450) (Key: cms_content:ec815af072c3ffe92168e7dffd8ef6a6)
2025-05-18 21:45:36,538 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for (|ENGD301|) Engineering Drawing & Design (49) (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65)
2025-05-18 21:45:36,539 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-18 21:45:36,540 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-18 21:45:36,540 - INFO - refresh_cache_script - Attempt 1/3 to fetch CMS content for Engineering - 2nd Orientation course (https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65)
2025-05-18 21:45:36,544 - INFO - scraping.cms - Fetching CMS course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-18 21:45:36,544 - INFO - scraping.cms - Fetching CMS course announcements for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-18 21:45:36,584 - INFO - scraping.cms - VOD '3 - Tutorial 3 (ws 3) (VoD)' ID '150675_f_1066977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:36,604 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:36,640 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65
2025-05-18 21:45:36,837 - INFO - utils.cache - Set PICKLE cache for key cms_content:82542a10b678a487bf3d8d0052c3f6f5 with expiry 3600 seconds
2025-05-18 21:45:36,837 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|ENGD301|) Engineering Drawing & Design (49) (Key: cms_content:82542a10b678a487bf3d8d0052c3f6f5)
2025-05-18 21:45:36,844 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:36,951 - INFO - scraping.cms - VOD '1 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:36,974 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65. Found 16 weeks.
2025-05-18 21:45:36,995 - INFO - scraping.cms - VOD '5 - Lecture 4 Video (VoD)' ID '150675_f_1061032' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:37,061 - INFO - utils.cache - Set PICKLE cache for key cms_content:075b4828d3fee66f0d27d4a0edfb1696 with expiry 3600 seconds
2025-05-18 21:45:37,062 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for Engineering - 2nd Orientation course (Key: cms_content:075b4828d3fee66f0d27d4a0edfb1696)
2025-05-18 21:45:37,285 - INFO - utils.cache - Set PICKLE cache for key cms_content:075b4828d3fee66f0d27d4a0edfb1696 with expiry 3600 seconds
2025-05-18 21:45:37,286 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for Engineering - 2nd Orientation course (Key: cms_content:075b4828d3fee66f0d27d4a0edfb1696)
2025-05-18 21:45:37,313 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65
2025-05-18 21:45:37,416 - INFO - scraping.cms - VOD '1 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:37,502 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65. Found 1 weeks.
2025-05-18 21:45:37,518 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:37,604 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65. Found 13 weeks.
2025-05-18 21:45:37,726 - INFO - utils.cache - Set PICKLE cache for key cms_content:24ac4b2ef071b508afcbda016918c8cc with expiry 3600 seconds
2025-05-18 21:45:37,727 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|MATH404|) Math IV (1072) (Key: cms_content:24ac4b2ef071b508afcbda016918c8cc)
2025-05-18 21:45:37,728 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65 (initiated by abobakr.bedda) successful.
2025-05-18 21:45:37,728 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65 (initiated by abobakr.bedda) successful.
2025-05-18 21:45:37,729 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65 (initiated by abobakr.bedda) successful.
2025-05-18 21:45:37,730 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65 (initiated by abobakr.bedda) successful.
2025-05-18 21:45:37,731 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65 (initiated by abobakr.bedda) successful.
2025-05-18 21:45:37,731 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65 (initiated by abobakr.bedda) successful.
2025-05-18 21:45:37,732 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65 (initiated by abobakr.bedda) successful.
2025-05-18 21:45:37,732 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65 (initiated by abobakr.bedda) successful.
2025-05-18 21:45:37,733 - INFO - refresh_cache_script - CMS Content processing summary for user abobakr.bedda: Processed for user abobakr.bedda: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-05-18 21:45:37,734 - INFO - refresh_cache_script - Finished processing for user: abobakr.bedda
2025-05-18 21:45:37,735 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65 (initiated by ahmed.abd-elhamid) successful.
2025-05-18 21:45:37,736 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65 (initiated by ahmed.abd-elhamid) successful.
2025-05-18 21:45:37,736 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65 (initiated by ahmed.abd-elhamid) successful.
2025-05-18 21:45:37,737 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65 (initiated by ahmed.abd-elhamid) successful.
2025-05-18 21:45:37,737 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65 (initiated by ahmed.abd-elhamid) successful.
2025-05-18 21:45:37,738 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65 (initiated by ahmed.abd-elhamid) successful.
2025-05-18 21:45:37,739 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65 (initiated by ahmed.abd-elhamid) successful.
2025-05-18 21:45:37,739 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65 (initiated by ahmed.abd-elhamid) successful.
2025-05-18 21:45:37,740 - INFO - refresh_cache_script - CMS Content processing summary for user ahmed.abd-elhamid: Processed for user ahmed.abd-elhamid: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-05-18 21:45:37,741 - INFO - refresh_cache_script - Finished processing for user: ahmed.abd-elhamid
2025-05-18 21:45:37,758 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:37,969 - INFO - utils.cache - Set PICKLE cache for key cms_content:075b4828d3fee66f0d27d4a0edfb1696 with expiry 3600 seconds
2025-05-18 21:45:37,970 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for Engineering - 2nd Orientation course (Key: cms_content:075b4828d3fee66f0d27d4a0edfb1696)
2025-05-18 21:45:38,070 - INFO - scraping.cms - VOD '3 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:38,096 - INFO - scraping.cms - VOD '3 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:38,449 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:38,739 - INFO - utils.cache - Set PICKLE cache for key cms_content:a65dad15c3d92e609d370a8134a22480 with expiry 3600 seconds
2025-05-18 21:45:38,740 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|MATH203|) Mathematics I (17) (Key: cms_content:a65dad15c3d92e609d370a8134a22480)
2025-05-18 21:45:38,800 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-18 21:45:38,802 - INFO - scraping.cms - VOD '3 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:38,886 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:39,089 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65. Found 9 weeks.
2025-05-18 21:45:39,214 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-18 21:45:39,215 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:39,511 - INFO - scraping.cms - VOD '1 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:39,757 - INFO - scraping.cms - VOD '1 - Revision Part I (VoD)' ID '150675_f_1123696' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:39,904 - INFO - scraping.cms - VOD '1 - kapitel 5 video 12 (VoD)' ID '150675_f_997901' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:39,945 - INFO - utils.cache - Set PICKLE cache for key cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc with expiry 3600 seconds
2025-05-18 21:45:39,946 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|DE202|) Basic German 2 (33) (Key: cms_content:ab32cc462f4fcb8a0663ebdbd94f5efc)
2025-05-18 21:45:40,124 - INFO - scraping.cms - VOD '1 - Lektion 24 Video 1 (VoD)' ID '150675_f_1073495' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:40,259 - INFO - scraping.cms - VOD '1 - Unit 6 Video 13 (VoD)' ID '150675_f_1004678' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:40,275 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65
2025-05-18 21:45:40,528 - INFO - scraping.cms - VOD '2 - Lektion 24 Video 1 Komplett (VoD)' ID '150675_f_1073500' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:40,632 - INFO - scraping.cms - VOD '1 - Video 14 (VoD)' ID '150675_f_1010657' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:40,969 - INFO - scraping.cms - VOD '2 - Video 15 (VoD)' ID '150675_f_1010661' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:41,099 - INFO - scraping.cms - VOD '3 - Lektion 24 Video 2 Komplett (VoD)' ID '150675_f_1073502' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:41,337 - INFO - scraping.cms - VOD '1 - Kapitel 5 Video 11 (VoD)' ID '150675_f_997894' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:41,392 - INFO - scraping.cms - VOD '1 - Lektion 23 Video 1 (VoD)' ID '150675_f_1073450' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:41,643 - INFO - scraping.cms - VOD '1 - Unit 4 Video 9 (VoD)' ID '150675_f_983448' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:41,712 - INFO - scraping.cms - VOD '2 - Lektion 23 Video 2 (VoD)' ID '150675_f_1073453' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:41,817 - INFO - scraping.cms - Finished parsing course content for mohamed.elsaadi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65. Found 18 weeks.
2025-05-18 21:45:41,845 - INFO - scraping.cms - Successfully scraped course announcements from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65
2025-05-18 21:45:41,916 - INFO - scraping.cms - VOD '2 - Unit 5 Video 10 (VoD)' ID '150675_f_983452' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:42,086 - INFO - scraping.cms - VOD '3 - Lektion 23 Video 3 (VoD)' ID '150675_f_1073456' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:42,209 - INFO - utils.cache - Set PICKLE cache for key cms_content:82542a10b678a487bf3d8d0052c3f6f5 with expiry 3600 seconds
2025-05-18 21:45:42,210 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|ENGD301|) Engineering Drawing & Design (49) (Key: cms_content:82542a10b678a487bf3d8d0052c3f6f5)
2025-05-18 21:45:42,211 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=16&sid=65 (initiated by mohamed.elsaadi) successful.
2025-05-18 21:45:42,212 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=450&sid=65 (initiated by mohamed.elsaadi) successful.
2025-05-18 21:45:42,212 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=79&sid=65 (initiated by mohamed.elsaadi) successful.
2025-05-18 21:45:42,213 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=33&sid=65 (initiated by mohamed.elsaadi) successful.
2025-05-18 21:45:42,214 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=19&sid=65 (initiated by mohamed.elsaadi) successful.
2025-05-18 21:45:42,215 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=17&sid=65 (initiated by mohamed.elsaadi) successful.
2025-05-18 21:45:42,215 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=49&sid=65 (initiated by mohamed.elsaadi) successful.
2025-05-18 21:45:42,216 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=-117&sid=65 (initiated by mohamed.elsaadi) successful.
2025-05-18 21:45:42,216 - INFO - refresh_cache_script - CMS Content processing summary for user mohamed.elsaadi: Processed for user mohamed.elsaadi: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-05-18 21:45:42,217 - INFO - refresh_cache_script - Finished processing for user: mohamed.elsaadi
2025-05-18 21:45:42,491 - INFO - scraping.cms - VOD '1 - Unit 4 Video 8 (VoD)' ID '150675_f_983472' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:42,646 - INFO - scraping.cms - VOD '4 - Lektion 23 Video 1 Komplett (VoD)' ID '150675_f_1073474' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:42,983 - INFO - scraping.cms - VOD '5 - Lektion 23 Video 2 Komplett (VoD)' ID '150675_f_1073481' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:43,056 - INFO - scraping.cms - VOD '1 - Unit 2 Video 5 (VoD)' ID '150675_f_967860' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:43,587 - INFO - scraping.cms - VOD '6 - Lektion 23 Video 3 Komplett (VoD)' ID '150675_f_1073486' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:43,740 - INFO - scraping.cms - VOD '2 - Unit 2 Video 6 (VoD)' ID '150675_f_967861' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:44,076 - INFO - scraping.cms - VOD '3 - Unit 2 Video 7 (VoD)' ID '150675_f_967862' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:44,149 - INFO - scraping.cms - VOD '7 - Lektion 23 Video 4 Komplett (VoD)' ID '150675_f_1073485' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:44,404 - INFO - scraping.cms - VOD '1 - Unit 1 Video 4 (VoD)' ID '150675_f_967843' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:44,694 - INFO - scraping.cms - VOD '1 - Video 2 (VoD)' ID '150675_f_962606' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:44,760 - INFO - scraping.cms - VOD '1 - Lektion 22 Video 1 (VoD)' ID '150675_f_1073442' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:45,112 - INFO - scraping.cms - VOD '2 - Lektion 22 Video 2 (VoD)' ID '150675_f_1073444' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:45,253 - INFO - scraping.cms - VOD '2 - Video 3 (VoD)' ID '150675_f_962713' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:45,621 - INFO - scraping.cms - VOD '1 - Video 1 (VoD)' ID '150675_f_962715' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:45,633 - INFO - scraping.cms - VOD '3 - Lektion 22 complete video 1 (VoD)' ID '150675_f_1073448' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:45,949 - INFO - scraping.cms - Finished parsing course content for nour.tantawi from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65. Found 36 weeks.
2025-05-18 21:45:45,959 - INFO - scraping.cms - VOD '4 - Lektion 22 complete video 2 (VoD)' ID '150675_f_1101615' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:46,423 - INFO - utils.cache - Set PICKLE cache for key cms_content:34c291bbefc813231601da76882a6b39 with expiry 3600 seconds
2025-05-18 21:45:46,424 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|DE404|) Basic German 4 (73) (Key: cms_content:34c291bbefc813231601da76882a6b39)
2025-05-18 21:45:46,425 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65 (initiated by nour.tantawi) successful.
2025-05-18 21:45:46,426 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65 (initiated by nour.tantawi) successful.
2025-05-18 21:45:46,427 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65 (initiated by nour.tantawi) successful.
2025-05-18 21:45:46,427 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65 (initiated by nour.tantawi) successful.
2025-05-18 21:45:46,428 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65 (initiated by nour.tantawi) successful.
2025-05-18 21:45:46,428 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65 (initiated by nour.tantawi) successful.
2025-05-18 21:45:46,429 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65 (initiated by nour.tantawi) successful.
2025-05-18 21:45:46,430 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65 (initiated by nour.tantawi) successful.
2025-05-18 21:45:46,430 - INFO - refresh_cache_script - CMS Content processing summary for user nour.tantawi: Processed for user nour.tantawi: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-05-18 21:45:46,431 - INFO - refresh_cache_script - Finished processing for user: nour.tantawi
2025-05-18 21:45:46,605 - INFO - scraping.cms - VOD '1 - Unit 21 Part 1 (VoD)' ID '150675_f_1063834' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:47,125 - INFO - scraping.cms - VOD '2 - Unit 21 Part 2 (VoD)' ID '150675_f_1067572' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:47,470 - INFO - scraping.cms - VOD '3 - Unit 21 (Complete Unit: Part 1) (VoD)' ID '150675_f_1094812' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:47,804 - INFO - scraping.cms - VOD '4 - Unit 21 (Complete Unit: Part 2) (VoD)' ID '150675_f_1094820' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:48,330 - INFO - scraping.cms - VOD '5 - Unit 21 (Complete Unit: Part 3) (VoD)' ID '150675_f_1094828' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:48,881 - INFO - scraping.cms - VOD '1 - Unit 20 (VoD)' ID '150675_f_1063828' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:49,499 - INFO - scraping.cms - VOD '2 - Unit 20 (Complete Unit) (VoD)' ID '150675_f_1092097' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:49,808 - INFO - scraping.cms - VOD '3 - K19 - V1 (VoD)' ID '150675_f_1062891' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:50,087 - INFO - scraping.cms - VOD '4 - Unit 19 Complete Unit (Part1) (VoD)' ID '150675_f_1066246' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:50,642 - INFO - scraping.cms - VOD '5 - Unit 19 Complete Unit (Part 2) (VoD)' ID '150675_f_1066249' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:50,973 - INFO - scraping.cms - VOD '1 - Revision 1 (VoD)' ID '150675_f_1124540' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:51,505 - INFO - scraping.cms - VOD '2 - Revision 2 (VoD)' ID '150675_f_1124541' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:51,801 - INFO - scraping.cms - VOD '4 - Model test DE3 AUDIO (VoD)' ID '150675_f_1124235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:52,384 - INFO - scraping.cms - VOD '1 - U18-V1 Blended (VoD)' ID '150675_f_1090464' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:52,982 - INFO - scraping.cms - VOD '2 - U18-V2 Blended (VoD)' ID '150675_f_1090082' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:53,254 - INFO - scraping.cms - VOD '3 - U18-V1 Complete (VoD)' ID '150675_f_1090084' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:53,538 - INFO - scraping.cms - VOD '4 - U18-V2 Complete (VoD)' ID '150675_f_1090083' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:54,054 - INFO - scraping.cms - VOD '1 - U17- V1 Blended (VoD)' ID '150675_f_1090058' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:54,572 - INFO - scraping.cms - VOD '2 - U17-V2 Blended (VoD)' ID '150675_f_1090062' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:54,877 - INFO - scraping.cms - VOD '3 - U17-V3 Blended (VoD)' ID '150675_f_1090064' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:55,392 - INFO - scraping.cms - VOD '4 - U17 - V1 Complete (VoD)' ID '150675_f_1090063' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:55,724 - INFO - scraping.cms - VOD '5 - U17 - V2 Complete (VoD)' ID '150675_f_1090067' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:56,301 - INFO - scraping.cms - VOD '6 - U17-V3 Complete (VoD)' ID '150675_f_1090071' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:56,826 - INFO - scraping.cms - VOD '1 - U16- V1 Blended (VoD)' ID '150675_f_1090053' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:57,383 - INFO - scraping.cms - VOD '2 - U16- V1 complete (VoD)' ID '150675_f_1090057' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:58,020 - INFO - scraping.cms - VOD '3 - U16-V2 complete (VoD)' ID '150675_f_1090462' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:58,607 - INFO - scraping.cms - VOD '1 - U13-complete Video1 (VoD)' ID '150675_f_1065580' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:58,923 - INFO - scraping.cms - VOD '2 - U13- complete Video2 (VoD)' ID '150675_f_1065582' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:59,536 - INFO - scraping.cms - VOD '1 - U15-V1 (VoD)' ID '150675_f_1065068' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:45:59,818 - INFO - scraping.cms - VOD '2 - U16-V1 (VoD)' ID '150675_f_1065069' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:00,400 - INFO - scraping.cms - VOD '3 - U15 V1 Complete (VoD)' ID '150675_f_1141997' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:00,786 - INFO - scraping.cms - VOD '1 - U14 - V1 (VoD)' ID '150675_f_1063454' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:01,457 - INFO - scraping.cms - VOD '2 - U14- V2 (VoD)' ID '150675_f_1065039' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:02,062 - INFO - scraping.cms - VOD '3 - U14-V3 (VoD)' ID '150675_f_1065060' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:02,582 - INFO - scraping.cms - VOD '4 - U14-V4 (VoD)' ID '150675_f_1065058' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:03,229 - INFO - scraping.cms - VOD '5 - U14 Complete Video (VoD)' ID '150675_f_1141802' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:03,581 - INFO - scraping.cms - VOD '6 - U14 Complete V2 (VoD)' ID '150675_f_1141799' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:04,186 - INFO - scraping.cms - VOD '1 - K13- V1 (VoD)' ID '150675_f_1062824' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:04,477 - INFO - scraping.cms - VOD '2 - K13 - V2 (VoD)' ID '150675_f_1062876' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:05,380 - INFO - scraping.cms - VOD '1 - Revision (Reading & Writing) (VoD)' ID '150675_f_1091975' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:05,911 - INFO - scraping.cms - VOD '2 - Revision (Grammar & Communicative Situations) (VoD)' ID '150675_f_1091977' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:06,485 - INFO - scraping.cms - VOD '3 - Revision (Unit 7-12) (VoD)' ID '150675_f_1091981' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:06,788 - INFO - scraping.cms - VOD '6 - Model Test Audio (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-1c18cd82-df3b-f5be-d25b-8e4a04d182f7' for access URL.
2025-05-18 21:46:06,789 - INFO - scraping.cms - VOD '1 - Unit 12 ( V1 Vocabulary) (VoD)' ID '150675_f_1091959' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:07,353 - INFO - scraping.cms - VOD '2 - Unit 12: (V2 Grammar) (VoD)' ID '150675_f_1091961' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:07,882 - INFO - scraping.cms - VOD '3 - Unit 12: Complete Unit (Part 1) (VoD)' ID '150675_f_1091966' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:08,203 - INFO - scraping.cms - VOD '4 - Unit 12: Complete Unit (Part 2) (VoD)' ID '150675_f_1091968' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:08,508 - INFO - scraping.cms - VOD '1 - Unit 11 (V1 Vocabulary) (VoD)' ID '150675_f_1091946' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:08,818 - INFO - scraping.cms - VOD '2 - Unit 11 (V2 Grammar) (VoD)' ID '150675_f_1091948' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:09,374 - INFO - scraping.cms - VOD '1 - Unit 9 Video 2 (Grammar) (VoD)' ID '150675_f_1074780' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:09,919 - INFO - scraping.cms - VOD '1 - Unit 10 Video 2 (Grammar) (VoD)' ID '150675_f_1090019' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:10,463 - INFO - scraping.cms - VOD '2 - Unit 10 Video 1 (Vocabulary) (VoD)' ID '150675_f_1090011' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:10,992 - INFO - scraping.cms - VOD '1 - Unit 8 Video 1 (Vocabulary & Grammar) (VoD)' ID '150675_f_1063616' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:11,277 - INFO - scraping.cms - VOD '2 - Unit 8 Complete Unit (Part 1) (VoD)' ID '150675_f_1066235' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:11,684 - INFO - scraping.cms - VOD '3 - Unit 8 Complete Unit (Part 2) (VoD)' ID '150675_f_1066242' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:12,255 - INFO - scraping.cms - VOD '1 - Unit 7 Video 2 (VoD)' ID '150675_f_1062907' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:12,609 - INFO - scraping.cms - VOD '2 - Unit 7 Complete Unit Video (VoD)' ID contains '-vod-'. Using direct ID '2aeb7b88513094e07f2404a6d13fedc4-vod-b1504537-f005-d66b-f365-d5140b9d48ce' for access URL.
2025-05-18 21:46:12,610 - INFO - scraping.cms - VOD '1 - Unit 9 Video 1 (Vocabulary) (VoD)' ID '150675_f_1074763' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:12,887 - INFO - scraping.cms - VOD '1 - Unit 7 Video 1 (VoD)' ID '150675_f_1062905' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:13,424 - INFO - scraping.cms - VOD '1 - kapitel 5 video 12 (VoD)' ID '150675_f_997901' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:14,009 - INFO - scraping.cms - VOD '1 - Unit 6 Video 13 (VoD)' ID '150675_f_1004678' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:14,531 - INFO - scraping.cms - VOD '1 - Video 14 (VoD)' ID '150675_f_1010657' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:15,090 - INFO - scraping.cms - VOD '2 - Video 15 (VoD)' ID '150675_f_1010661' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:15,661 - INFO - scraping.cms - VOD '1 - Kapitel 5 Video 11 (VoD)' ID '150675_f_997894' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:15,955 - INFO - scraping.cms - VOD '1 - Unit 4 Video 9 (VoD)' ID '150675_f_983448' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:16,239 - INFO - scraping.cms - VOD '2 - Unit 5 Video 10 (VoD)' ID '150675_f_983452' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:16,818 - INFO - scraping.cms - VOD '1 - Unit 4 Video 8 (VoD)' ID '150675_f_983472' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:17,388 - INFO - scraping.cms - VOD '1 - Unit 2 Video 5 (VoD)' ID '150675_f_967860' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:17,946 - INFO - scraping.cms - VOD '2 - Unit 2 Video 6 (VoD)' ID '150675_f_967861' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:18,211 - INFO - scraping.cms - VOD '3 - Unit 2 Video 7 (VoD)' ID '150675_f_967862' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:18,780 - INFO - scraping.cms - VOD '1 - Unit 1 Video 4 (VoD)' ID '150675_f_967843' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:19,404 - INFO - scraping.cms - VOD '1 - Video 2 (VoD)' ID '150675_f_962606' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:19,680 - INFO - scraping.cms - VOD '2 - Video 3 (VoD)' ID '150675_f_962713' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:20,206 - INFO - scraping.cms - VOD '1 - Video 1 (VoD)' ID '150675_f_962715' lacks '-vod-'. Fetching actual ID via Dacast API...
2025-05-18 21:46:20,804 - INFO - scraping.cms - Finished parsing course content for malak.mohamedelkady from https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65. Found 36 weeks.
2025-05-18 21:46:21,234 - INFO - utils.cache - Set PICKLE cache for key cms_content:34c291bbefc813231601da76882a6b39 with expiry 3600 seconds
2025-05-18 21:46:21,235 - INFO - refresh_cache_script - Successfully refreshed GLOBAL CMS content cache for (|DE404|) Basic German 4 (73) (Key: cms_content:34c291bbefc813231601da76882a6b39)
2025-05-18 21:46:21,236 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=402&sid=65 (initiated by malak.mohamedelkady) successful.
2025-05-18 21:46:21,237 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=486&sid=65 (initiated by malak.mohamedelkady) successful.
2025-05-18 21:46:21,237 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2488&sid=65 (initiated by malak.mohamedelkady) successful.
2025-05-18 21:46:21,238 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=510&sid=65 (initiated by malak.mohamedelkady) successful.
2025-05-18 21:46:21,239 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=2708&sid=65 (initiated by malak.mohamedelkady) successful.
2025-05-18 21:46:21,239 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=34&sid=65 (initiated by malak.mohamedelkady) successful.
2025-05-18 21:46:21,240 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=73&sid=65 (initiated by malak.mohamedelkady) successful.
2025-05-18 21:46:21,240 - INFO - refresh_cache_script - Global refresh for https://cms.guc.edu.eg/apps/student/courseviewstn.aspx?id=1072&sid=65 (initiated by malak.mohamedelkady) successful.
2025-05-18 21:46:21,241 - INFO - refresh_cache_script - CMS Content processing summary for user malak.mohamedelkady: Processed for user malak.mohamedelkady: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-05-18 21:46:21,242 - INFO - refresh_cache_script - Finished processing for user: malak.mohamedelkady
2025-05-18 21:46:21,242 - INFO - refresh_cache_script - --- Cache Refresh Summary ---
2025-05-18 21:46:21,243 - INFO - refresh_cache_script - User: malak.mohamedelkady -> cms_content: Processed for user malak.mohamedelkady: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-05-18 21:46:21,243 - INFO - refresh_cache_script - User: malek.amer -> cms_content: Processed for user malek.amer: Globally Updated Now=7, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-05-18 21:46:21,244 - INFO - refresh_cache_script - User: ahmed.abd-elhamid -> cms_content: Processed for user ahmed.abd-elhamid: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-05-18 21:46:21,244 - INFO - refresh_cache_script - User: seif.elkady -> cms_content: Processed for user seif.elkady: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-05-18 21:46:21,245 - INFO - refresh_cache_script - User: abobakr.bedda -> cms_content: Processed for user abobakr.bedda: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-05-18 21:46:21,245 - INFO - refresh_cache_script - User: nour.tantawi -> cms_content: Processed for user nour.tantawi: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-05-18 21:46:21,246 - INFO - refresh_cache_script - User: mohamed.elsaadi -> cms_content: Processed for user mohamed.elsaadi: Globally Updated Now=8, Skipped (already updated this run)=0, Global Fails Now=0, Skipped (fetch/URL issues)=0
2025-05-18 21:46:21,246 - INFO - refresh_cache_script - --- Cache Refresh Script Finished: 2025-05-18T21:46:21.246908 (Duration: 0:01:27.191744) ---
2025-05-18 21:46:21,247 - INFO - refresh_cache_script - CMS Content Global Summary: Total unique courses successfully refreshed and cached this run = 17
2025-05-18 21:46:21,247 - INFO - refresh_cache_script - Overall Items Summary (excluding CMS content courses): Updated=0, Skipped=0, Failed=0
